import { useState, useEffect } from "react";
import { usePortfolioContent } from "./usePortfolioContent";

export interface HeroContent {
  name: string;
  profession: string;
  welcomeText: string;
  description: string;
  buttonText: string;
  profileImage: string;
  typingTexts: string[];
  professions: string[];
}

// Default hero content as fallback
const defaultHeroContent: HeroContent = {
  name: "Kenenis Dev",
  profession: "Full Stack Developer",
  welcomeText: "WELCOME TO MY WORLD",
  description: "I'm a passionate full stack developer specializing in building exceptional digital experiences.",
  buttonText: "More About Me",
  profileImage: "https://i.postimg.cc/FH1L6nNj/1724878552606-2.jpg",
  typingTexts: ["Full Stack Developer", "UI/UX Designer", "Web Developer"],
  professions: ["Full Stack Developer", "UI/UX Designer", "Web Developer", "Software Engineer"],
};

export const useHeroContent = () => {
  const { content, isLoading, error } = usePortfolioContent();
  const [heroContent, setHeroContent] = useState<HeroContent>(defaultHeroContent);

  useEffect(() => {
    if (content && content.length > 0) {
      const heroData = content.find(item => item.section === "hero")?.content;
      if (heroData) {
        setHeroContent(prev => ({
          ...prev,
          ...heroData,
        }));
      }
    }
  }, [content]);

  return {
    heroContent,
    isLoading,
    error,
  };
};
