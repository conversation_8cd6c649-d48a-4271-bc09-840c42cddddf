<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e293b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f172a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="k-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff014f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f9004d;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="16" fill="url(#bg-gradient)"/>
  
  <!-- Modern K letter -->
  <g transform="translate(8, 6)">
    <!-- Left vertical line of K -->
    <rect x="0" y="0" width="3" height="20" fill="url(#k-gradient)" rx="1.5"/>
    
    <!-- Upper diagonal of K -->
    <path d="M4 0 L16 10 L13.5 12.5 L4 5.5 Z" fill="url(#k-gradient)"/>
    
    <!-- Lower diagonal of K -->
    <path d="M4 14.5 L13.5 7.5 L16 10 L4 20 Z" fill="url(#k-gradient)"/>
  </g>
  
  <!-- Subtle glow effect -->
  <circle cx="16" cy="16" r="15" fill="none" stroke="url(#k-gradient)" stroke-width="0.5" opacity="0.3"/>
</svg>
