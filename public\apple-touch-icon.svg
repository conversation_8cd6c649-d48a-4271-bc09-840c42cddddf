<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e293b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f172a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="k-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff014f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f9004d;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background rounded rectangle for iOS -->
  <rect x="0" y="0" width="180" height="180" rx="40" ry="40" fill="url(#bg-gradient)"/>
  
  <!-- Modern K letter (scaled up) -->
  <g transform="translate(45, 35)">
    <!-- Left vertical line of K -->
    <rect x="0" y="0" width="18" height="110" fill="url(#k-gradient)" rx="9"/>
    
    <!-- Upper diagonal of K -->
    <path d="M24 0 L90 55 L76.5 68.75 L24 30.25 Z" fill="url(#k-gradient)"/>
    
    <!-- Lower diagonal of K -->
    <path d="M24 79.75 L76.5 41.25 L90 55 L24 110 Z" fill="url(#k-gradient)"/>
  </g>
  
  <!-- Subtle glow effect -->
  <rect x="2" y="2" width="176" height="176" rx="38" ry="38" fill="none" stroke="url(#k-gradient)" stroke-width="2" opacity="0.3"/>
</svg>
