<svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e293b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f172a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="k-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff014f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f9004d;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background rounded rectangle -->
  <rect x="0" y="0" width="192" height="192" rx="48" ry="48" fill="url(#bg-gradient)"/>
  
  <!-- Modern K letter (scaled up) -->
  <g transform="translate(48, 38)">
    <!-- Left vertical line of K -->
    <rect x="0" y="0" width="20" height="116" fill="url(#k-gradient)" rx="10"/>
    
    <!-- Upper diagonal of K -->
    <path d="M26 0 L96 58 L82 72 L26 32 Z" fill="url(#k-gradient)"/>
    
    <!-- Lower diagonal of K -->
    <path d="M26 84 L82 44 L96 58 L26 116 Z" fill="url(#k-gradient)"/>
  </g>
  
  <!-- Subtle glow effect -->
  <rect x="4" y="4" width="184" height="184" rx="44" ry="44" fill="none" stroke="url(#k-gradient)" stroke-width="2" opacity="0.3"/>
</svg>
