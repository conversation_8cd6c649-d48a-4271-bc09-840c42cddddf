import React, { useState, useEffect } from "react";
import {
  Phone,
  Mail,
  MapPin,
  Send,
  MessageCircle,
  User,
  AtSign,
  Hash,
  FileText,
  Download,
} from "lucide-react";
import { toast } from "sonner";
import { useMessages } from "@/hooks/useMessages";
import { useCVDownload } from "@/hooks/useCVDownload";
import { usePortfolioContent } from "@/hooks/usePortfolioContent";

const Contact = () => {
  // Database hook for sending messages
  const { createMessage, isCreating } = useMessages();
  const { downloadCV, isDownloadEnabled } = useCVDownload();
  const { content } = usePortfolioContent();

  // Contact info from database with fallback
  const [contactData, setContactData] = useState({
    phone: "+90 551 898 60 38",
    email: "<EMAIL>",
    address: "Remote Developer",
    availability: "Available for work",
    socialLinks: {
      linkedin: "",
      github: "",
      twitter: "",
      instagram: "",
      facebook: "",
    },
    socialVisibility: {
      linkedin: true,
      github: true,
      twitter: true,
      instagram: true,
      facebook: true,
    },
  });

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
  });

  // Load contact info from database
  useEffect(() => {
    if (content && content.length > 0) {
      const contactInfo = content.find(item => item.section === "contact")?.content;
      if (contactInfo) {
        setContactData((prev) => ({ ...prev, ...contactInfo }));
      }
    }
  }, [content]);

  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [visibleElements, setVisibleElements] = useState<number[]>([]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const elementId = parseInt(
              entry.target.getAttribute("data-element-id") || "0"
            );
            setVisibleElements((prev) => [...prev, elementId]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = document.querySelectorAll(".contact-animate");
    elements.forEach((element) => observer.observe(element));

    return () => observer.disconnect();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (
      !formData.name ||
      !formData.email ||
      !formData.subject ||
      !formData.message
    ) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error("Please enter a valid email address");
      return;
    }

    try {
      // Send message to database
      await createMessage({
        name: formData.name,
        email: formData.email,
        subject: formData.subject,
        message: formData.message,
        phone: formData.phone || null,
        status: "new",
        priority: "medium",
      });

      // Reset form on success
      setFormData({
        name: "",
        email: "",
        phone: "",
        subject: "",
        message: "",
      });

      toast.success("Message sent successfully!", {
        description: "Thank you for reaching out! I'll get back to you soon.",
        duration: 4000,
      });
    } catch (error) {
      toast.error("Failed to send message. Please try again.");
    }
  };

  // Enhanced contact info data from database
  const contactInfo = [
    {
      id: 1,
      icon: <Phone size={16} />,
      title: "Phone",
      content: contactData.phone,
      href: `tel:${contactData.phone.replace(/\s/g, '')}`,
      description: "Call me anytime",
      color: "from-blue-500 to-cyan-500",
    },
    {
      id: 2,
      icon: <Mail size={16} />,
      title: "Email",
      content: contactData.email,
      href: `mailto:${contactData.email}`,
      description: "Send me an email",
      color: "from-red-500 to-pink-500",
    },
    {
      id: 3,
      icon: <MapPin size={16} />,
      title: "Address",
      content: contactData.address,
      href: "https://maps.google.com",
      description: contactData.availability,
      color: "from-green-500 to-emerald-500",
    },
  ];

  // Form field configurations
  const formFields = [
    {
      id: "name",
      label: "Your Name",
      type: "text",
      icon: <User size={20} />,
      placeholder: "Enter your full name",
      required: true,
      colSpan: "md:col-span-1",
    },
    {
      id: "email",
      label: "Email Address",
      type: "email",
      icon: <AtSign size={20} />,
      placeholder: "Enter your email address",
      required: true,
      colSpan: "md:col-span-1",
    },
    {
      id: "phone",
      label: "Phone Number",
      type: "tel",
      icon: <Phone size={20} />,
      placeholder: "Enter your phone number",
      required: false,
      colSpan: "md:col-span-1",
    },
    {
      id: "subject",
      label: "Subject",
      type: "text",
      icon: <Hash size={20} />,
      placeholder: "What is this about?",
      required: true,
      colSpan: "md:col-span-1",
    },
  ];

  return (
    <section
      id="contact"
      className="section bg-dark-darker relative overflow-hidden"
    >
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0">
        <div className="contact-bg-gradient"></div>
        <div className="contact-floating-particles"></div>
      </div>

      <div className="container mx-auto relative z-10">
        {/* Enhanced Header */}
        <div className="text-center mb-16">
          <div className="enhanced-contact-header">
            <h2 className="enhanced-contact-title">
              <span className="contact-title-text">Contact Me</span>
              <div className="contact-title-glow"></div>
            </h2>
            <div className="contact-description-container">
              <p className="contact-description">
                Feel free to contact me for any project or collaboration.
              </p>
              <div className="contact-description-accent"></div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Enhanced Contact Info */}
          <div className="space-y-6">
            {contactInfo.map((info, index) => (
              <div
                key={info.id}
                data-element-id={info.id}
                className={`contact-animate enhanced-contact-card ${
                  visibleElements.includes(info.id) ? "visible" : ""
                }`}
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <a
                  href={info.href}
                  className="contact-card-link"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {/* Card Background Effects */}
                  <div className="contact-card-bg"></div>
                  <div className="contact-card-glow"></div>
                  <div className="contact-card-particles"></div>

                  {/* Icon Container */}
                  <div
                    className={`contact-icon-container bg-gradient-to-br ${info.color}`}
                  >
                    <div className="contact-icon-glow"></div>
                    <div className="contact-icon">{info.icon}</div>
                    <div className="contact-icon-ring"></div>
                  </div>

                  {/* Content */}
                  <div className="contact-card-content">
                    <h3 className="contact-card-title">{info.title}</h3>
                    <p className="contact-card-description">
                      {info.description}
                    </p>
                    <p className="contact-card-value">{info.content}</p>
                  </div>

                  {/* Hover Effects */}
                  <div className="contact-card-hover-overlay"></div>
                </a>
              </div>
            ))}

            {/* Enhanced Download CV Button */}
            {isDownloadEnabled && (
              <div className="mt-8">
                <div className="relative group">
                  {/* Button Glow Background */}
                  <div className="absolute inset-0 bg-gradient-to-r from-red via-purple-500 to-red rounded-xl blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-500 animate-pulse"></div>

                  <button
                    onClick={downloadCV}
                    className="relative block w-full bg-gradient-to-r from-red to-red-600 hover:from-red-600 hover:to-red text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-red/25 border border-red/20 hover:border-red/40"
                  >
                    <div className="flex items-center justify-center gap-3">
                      <div className="relative">
                        <Download
                          size={20}
                          className="transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12"
                        />
                        {/* Icon Glow */}
                        <div className="absolute inset-0 bg-white/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>
                      <span className="text-lg font-bold tracking-wide">
                        Download CV
                      </span>

                      {/* Animated Arrow */}
                      <div className="w-0 group-hover:w-6 transition-all duration-300 overflow-hidden">
                        <div className="text-white/80">→</div>
                      </div>
                    </div>

                    {/* Button Shine Effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Enhanced Contact Form */}
          <div className="lg:col-span-2">
            <div
              data-element-id={4}
              className={`contact-animate enhanced-contact-form ${
                visibleElements.includes(4) ? "visible" : ""
              }`}
              style={{ animationDelay: "0.6s" }}
            >
              <form onSubmit={handleSubmit} className="contact-form">
                {/* Form Background Effects */}
                <div className="form-bg-gradient"></div>
                <div className="form-particles"></div>

                {/* Form Header */}
                <div className="form-header">
                  <div className="form-header-icon">
                    <MessageCircle size={28} className="text-red" />
                  </div>
                  <h3 className="form-title">Let's Work Together</h3>
                  <p className="form-subtitle">
                    Send me a message and I'll get back to you soon
                  </p>
                </div>

                {/* Form Fields Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  {formFields.map((field, index) => (
                    <div
                      key={field.id}
                      className={`enhanced-form-field ${field.colSpan}`}
                    >
                      <label htmlFor={field.id} className="enhanced-form-label">
                        <span className="label-icon">{field.icon}</span>
                        <span className="label-text">{field.label}</span>
                        {field.required && (
                          <span className="label-required">*</span>
                        )}
                      </label>
                      <div className="form-input-container">
                        <input
                          type={field.type}
                          id={field.id}
                          name={field.id}
                          value={formData[field.id as keyof typeof formData]}
                          onChange={handleChange}
                          onFocus={() => setFocusedField(field.id)}
                          onBlur={() => setFocusedField(null)}
                          required={field.required}
                          placeholder={field.placeholder}
                          className={`enhanced-form-input ${
                            focusedField === field.id ? "focused" : ""
                          }`}
                        />
                        <div className="input-glow"></div>
                        <div className="input-border-animation"></div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Message Field */}
                <div className="enhanced-form-field mb-8">
                  <label htmlFor="message" className="enhanced-form-label">
                    <span className="label-icon">
                      <FileText size={20} />
                    </span>
                    <span className="label-text">Your Message</span>
                    <span className="label-required">*</span>
                  </label>
                  <div className="form-input-container">
                    <textarea
                      id="message"
                      name="message"
                      rows={5}
                      value={formData.message}
                      onChange={handleChange}
                      onFocus={() => setFocusedField("message")}
                      onBlur={() => setFocusedField(null)}
                      required
                      placeholder="Tell me about your project..."
                      className={`enhanced-form-textarea ${
                        focusedField === "message" ? "focused" : ""
                      }`}
                    ></textarea>
                    <div className="input-glow"></div>
                    <div className="input-border-animation"></div>
                  </div>
                </div>

                {/* Enhanced Submit Button */}
                <div className="form-submit-container">
                  <button
                    type="submit"
                    className={`enhanced-submit-btn ${
                      isCreating ? "submitting" : ""
                    }`}
                    disabled={isCreating}
                  >
                    <div className="btn-bg-gradient"></div>
                    <div className="btn-particles"></div>
                    <div className="btn-content">
                      <span className="btn-icon">
                        {isCreating ? (
                          <div className="loading-spinner"></div>
                        ) : (
                          <Send size={20} />
                        )}
                      </span>
                      <span className="btn-text">
                        {isCreating ? "Sending Message..." : "Send Message"}
                      </span>
                    </div>
                    <div className="btn-glow"></div>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
