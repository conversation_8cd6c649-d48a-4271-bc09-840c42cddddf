import React, { useState, useEffect } from "react";
import {
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Github,
  MapPin,
  Calendar,
  <PERSON>rkles,
  Star,
} from "lucide-react";
import { useHeroContent } from "@/hooks/useHeroContent";
import { supabase } from "@/integrations/supabase/client";

const Sidebar = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const { heroContent } = useHeroContent();

  // Contact info from database with fallback
  const [contactData, setContactData] = useState({
    phone: "+90 551 898 60 38",
    email: "<EMAIL>",
    address: "Remote Developer",
    socialLinks: {
      linkedin: "",
      github: "",
      twitter: "",
      instagram: "",
      facebook: "",
    },
    socialVisibility: {
      linkedin: true,
      github: true,
      twitter: true,
      instagram: true,
      facebook: false,
    },
  });

  // Fetch contact data from database
  useEffect(() => {
    const fetchContactData = async () => {
      try {
        const { data, error } = await supabase
          .from("portfolio_content")
          .select("content")
          .eq("section", "contact")
          .single();

        if (data && !error) {
          setContactData(data.content);
        }
      } catch (error) {
        console.error("Error fetching contact data:", error);
      }
    };

    fetchContactData();
  }, []);

  // Social links configuration
  const socialLinks = [
    {
      icon: Linkedin,
      href: contactData.socialLinks?.linkedin || "#",
      label: "LinkedIn",
      color: "bg-blue-600/20",
      visible: contactData.socialVisibility?.linkedin
    },
    {
      icon: Github,
      href: contactData.socialLinks?.github || "#",
      label: "GitHub",
      color: "bg-gray-600/20",
      visible: contactData.socialVisibility?.github
    },
    {
      icon: Twitter,
      href: contactData.socialLinks?.twitter || "#",
      label: "Twitter",
      color: "bg-sky-500/20",
      visible: contactData.socialVisibility?.twitter
    },
    {
      icon: Instagram,
      href: contactData.socialLinks?.instagram || "#",
      label: "Instagram",
      color: "bg-gradient-to-r from-purple-500/20 to-pink-500/20",
      visible: contactData.socialVisibility?.instagram
    },
    {
      icon: Facebook,
      href: contactData.socialLinks?.facebook || "#",
      label: "Facebook",
      color: "bg-blue-500/20",
      visible: contactData.socialVisibility?.facebook
    },
  ].filter(link => link.href !== "#" && link.visible); // Only show links that are configured and visible

  // Stunning background images for carousel
  const backgroundImages = [
    "https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=800&h=1200&fit=crop&auto=format&q=80", // Galaxy/space
    "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=1200&fit=crop&auto=format&q=80", // Mountain landscape
    "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=800&h=1200&fit=crop&auto=format&q=80", // Nebula
    "https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=800&h=1200&fit=crop&auto=format&q=80", // Ocean waves
    "https://images.unsplash.com/photo-1464802686167-b939a6910659?w=800&h=1200&fit=crop&auto=format&q=80", // Stars/cosmos
  ];

  // Carousel effect - change image every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex(
        (prevIndex) => (prevIndex + 1) % backgroundImages.length
      );
    }, 5000);

    return () => clearInterval(interval);
  }, [backgroundImages.length]);

  return (
    <div className="sidebar-container bg-dark-lighter w-full lg:w-[380px] lg:h-screen lg:fixed lg:top-0 lg:left-0 lg:overflow-y-auto lg:scrollbar-hide relative">
      {/* Carousel Background Images */}
      <div className="absolute inset-0 overflow-hidden">
        {backgroundImages.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-2000 ease-in-out ${
              index === currentImageIndex ? "opacity-30" : "opacity-0"
            }`}
            style={{
              backgroundImage: `url(${image})`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
            }}
          />
        ))}
        {/* Overlay to maintain readability */}
        <div className="absolute inset-0 bg-white/70 dark:bg-dark-lighter/60"></div>
      </div>

      {/* Moving Galaxy Animation */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Galaxy Stars */}
        <div className="galaxy-container">
          {[...Array(20)].map((_, i) => (
            <div
              key={`star-${i}`}
              className="galaxy-star"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${3 + Math.random() * 4}s`,
              }}
            />
          ))}
        </div>

        {/* Moving Galaxy Spiral */}
        <div className="galaxy-spiral">
          <div className="spiral-arm spiral-arm-1"></div>
          <div className="spiral-arm spiral-arm-2"></div>
          <div className="spiral-arm spiral-arm-3"></div>
        </div>

        {/* Floating Cosmic Particles */}
        <div className="cosmic-particles">
          {[...Array(15)].map((_, i) => (
            <div
              key={`particle-${i}`}
              className="cosmic-particle"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${8 + Math.random() * 6}s`,
              }}
            />
          ))}
        </div>
      </div>

      {/* Enhanced Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-red/5 via-transparent to-purple-500/5 pointer-events-none"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(255,1,79,0.03)_0%,transparent_70%)] pointer-events-none"></div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-8 w-1 h-1 bg-red/40 rounded-full animate-pulse"></div>
        <div className="absolute top-40 right-12 w-1.5 h-1.5 bg-purple-400/30 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute top-60 left-16 w-0.5 h-0.5 bg-blue-400/40 rounded-full animate-pulse delay-2000"></div>
        <div className="absolute bottom-40 right-8 w-1 h-1 bg-red/30 rounded-full animate-pulse delay-3000"></div>
        <div className="absolute bottom-60 left-12 w-1.5 h-1.5 bg-purple-400/20 rounded-full animate-pulse delay-500"></div>
      </div>

      <div className="flex flex-col min-h-full relative z-10 p-8 justify-center">
        {/* Enhanced Profile Image and Name */}
        <div className="text-center mb-8 relative mt-[100px]">
          {/* Profile Image with Enhanced Effects */}
          <div className="relative mx-auto w-40 h-40 mb-5 group">
            {/* Outer Glow Ring */}
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-red via-white to-red p-0.5 animate-spin-slower">
              <div className="w-full h-full rounded-full bg-dark-lighter"></div>
            </div>

            {/* Main Image Container */}
            <div className="absolute inset-0.5 rounded-full overflow-hidden ring-2 ring-red/30 group-hover:ring-red/50 transition-all duration-500">
              <img
                src={heroContent.profileImage}
                alt={heroContent.name}
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
              />

              {/* Image Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-red/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>

            {/* Floating Stars */}
            <Star className="absolute -top-2 -right-2 w-4 h-4 text-yellow-400 animate-pulse" />
            <Sparkles className="absolute -bottom-1 -left-2 w-3 h-3 text-purple-400 animate-pulse delay-1000" />
          </div>

          {/* Enhanced Name and Title */}
          <div className="relative">
            <h1 className="text-slate-800 dark:text-white text-2xl font-bold mb-2 bg-gradient-to-r from-slate-800 via-red to-slate-800 dark:from-white dark:via-red dark:to-white bg-clip-text text-transparent bg-300% animate-gradient-x">
              {heroContent.name}
            </h1>
            <div className="relative">
              <div className="typing-text mt-2 text-slate-600 dark:text-gray-300 text-center mx-auto max-w-[220px] text-base font-medium">
                <span className="bg-gradient-to-r from-slate-600 to-red dark:from-gray-300 dark:to-red bg-clip-text text-transparent">
                  {heroContent.profession}
                </span>
              </div>
              {/* Underline Effect */}
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-gradient-to-r from-transparent via-red to-transparent animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Enhanced Social Icons */}
        <div className="flex justify-center gap-3 mb-8">
          {socialLinks.map((social, index) => {
            const IconComponent = social.icon;
            return (
              <a
                key={index}
                href={social.href}
                target="_blank"
                rel="noopener noreferrer"
                className="enhanced-social-icon group"
                aria-label={social.label}
              >
                <div className="relative">
                  <IconComponent
                    size={20}
                    className="relative z-10 transition-transform duration-300 group-hover:scale-110"
                  />
                  <div className={`absolute inset-0 ${social.color} rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm`}></div>
                </div>
              </a>
            );
          })}
        </div>

        {/* Enhanced Contact Details */}
        <div className="space-y-4 mb-8">
          {/* Location Card */}
          <div className="enhanced-contact-card group">
            <div className="relative">
              {/* Background Glow */}
              <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <div className="relative flex items-center gap-4 p-4">
                <div className="relative">
                  {/* Icon Container */}
                  <div className="min-w-12 h-12 rounded-xl bg-gradient-to-br from-green-500/20 to-emerald-500/20 flex items-center justify-center text-green-400 group-hover:scale-110 transition-transform duration-300 border border-green-500/20">
                    <MapPin size={20} className="relative z-10" />
                    {/* Icon Glow */}
                    <div className="absolute inset-0 bg-green-500/20 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                  {/* Floating Dot */}
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                </div>

                <div className="flex-1">
                  <p className="text-sm text-slate-500 dark:text-gray-200 mb-1 font-medium">
                    Location
                  </p>
                  <p className="text-slate-700 dark:text-gray-100 font-semibold text-base group-hover:text-green-400 transition-colors duration-300">
                    New York, USA
                  </p>
                </div>

                {/* Decorative Element */}
                <div className="w-1 h-8 bg-gradient-to-b from-green-500/50 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </div>
          </div>

          {/* Birthday Card */}
          <div className="enhanced-contact-card group">
            <div className="relative">
              {/* Background Glow */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <div className="relative flex items-center gap-4 p-4">
                <div className="relative">
                  {/* Icon Container */}
                  <div className="min-w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center text-purple-400 group-hover:scale-110 transition-transform duration-300 border border-purple-500/20">
                    <Calendar size={20} className="relative z-10" />
                    {/* Icon Glow */}
                    <div className="absolute inset-0 bg-purple-500/20 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                  {/* Floating Sparkle */}
                  <Sparkles className="absolute -top-1 -right-1 w-3 h-3 text-pink-400 animate-pulse" />
                </div>

                <div className="flex-1">
                  <p className="text-sm text-slate-500 dark:text-gray-200 mb-1 font-medium">
                    Birthday
                  </p>
                  <p className="text-slate-700 dark:text-gray-100 font-semibold text-base group-hover:text-purple-400 transition-colors duration-300">
                    May 27, 1990
                  </p>
                </div>

                {/* Decorative Element */}
                <div className="w-1 h-8 bg-gradient-to-b from-purple-500/50 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
