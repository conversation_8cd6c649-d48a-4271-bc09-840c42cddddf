
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title><PERSON><PERSON><PERSON> (Kenenis) - Full Stack Developer & UI/UX Designer | Bursa, Turkey</title>
    <meta name="description" content="<PERSON><PERSON><PERSON> (Kenenis) - Expert Full Stack Developer based in Bursa, Turkey, specializing in React, Node.js, TypeScript & UI/UX Design. 2+ years experience, 20+ projects completed. Available for remote work worldwide." />
    <meta name="author" content="Kenenisa Kero" />
    <meta name="keywords" content="Kenenisa Kero, Kenenis, Full Stack Developer, React Developer, Node.js, TypeScript, UI/UX Designer, Web Developer, Software Engineer, Frontend Developer, Backend Developer, JavaScript, HTML, CSS, Portfolio, Freelancer, Turkey Developer, Bursa Developer, Turkish Web Developer, European Developer" />
    <link rel="canonical" href="https://kenenis.com/" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.svg" />
    <link rel="icon" type="image/svg+xml" sizes="32x32" href="/favicon-32x32.svg" />
    <link rel="icon" type="image/svg+xml" sizes="16x16" href="/favicon-16x16.svg" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Theme colors -->
    <meta name="theme-color" content="#ff014f" />
    <meta name="msapplication-TileColor" content="#1e293b" />

    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Kenenis Dev" />
    <meta name="application-name" content="Kenenis Dev Portfolio" />
    <meta name="msapplication-starturl" content="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, viewport-fit=cover" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://kenenis.com/" />
    <meta property="og:title" content="Kenenisa Kero (Kenenis) - Full Stack Developer & UI/UX Designer | Bursa, Turkey" />
    <meta property="og:description" content="Expert Full Stack Developer based in Bursa, Turkey, specializing in React, Node.js, TypeScript & UI/UX Design. 2+ years experience, 20+ projects completed. Available for remote work worldwide." />
    <meta property="og:image" content="https://kenenis.com/og-image.svg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:type" content="image/svg+xml" />
    <meta property="og:site_name" content="Kenenisa Kero Portfolio" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content="https://kenenis.com/" />
    <meta name="twitter:title" content="Kenenisa Kero (Kenenis) - Full Stack Developer & UI/UX Designer | Bursa, Turkey" />
    <meta name="twitter:description" content="Expert Full Stack Developer based in Bursa, Turkey, specializing in React, Node.js, TypeScript & UI/UX Design. 2+ years experience, 20+ projects completed." />
    <meta name="twitter:image" content="https://kenenis.com/og-image.svg" />
    <meta name="twitter:image:alt" content="Kenenisa Kero (Kenenis) Portfolio Logo" />
    <meta name="twitter:creator" content="@kenenis" />
    <meta name="twitter:site" content="@kenenis" />

    <!-- Additional meta tags for better social sharing -->
    <meta property="og:locale" content="en_US" />
    <meta property="og:image:alt" content="Kenenisa Kero (Kenenis) Portfolio Logo" />
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />

    <!-- Additional SEO Meta Tags -->
    <meta name="geo.region" content="TR-16" />
    <meta name="geo.country" content="Turkey" />
    <meta name="geo.placename" content="Bursa" />
    <meta name="geo.position" content="40.1826;29.0665" />
    <meta name="ICBM" content="40.1826, 29.0665" />
    <meta name="language" content="English" />
    <meta name="revisit-after" content="7 days" />
    <meta name="rating" content="general" />
    <meta name="distribution" content="global" />

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Kenenisa Kero",
      "alternateName": "Kenenis",
      "url": "https://kenenis.com",
      "image": "https://kenenis.com/og-image.svg",
      "sameAs": [
        "https://github.com/kenenis",
        "https://linkedin.com/in/kenenis",
        "https://twitter.com/kenenis"
      ],
      "jobTitle": "Full Stack Developer",
      "worksFor": {
        "@type": "Organization",
        "name": "Freelance"
      },
      "alumniOf": {
        "@type": "Organization",
        "name": "Self-taught Developer"
      },
      "knowsAbout": [
        "React.js",
        "Node.js",
        "TypeScript",
        "JavaScript",
        "HTML",
        "CSS",
        "UI/UX Design",
        "Full Stack Development",
        "Web Development",
        "Software Engineering"
      ],
      "description": "Expert Full Stack Developer based in Bursa, Turkey, specializing in React, Node.js, TypeScript & UI/UX Design with 2+ years experience and 20+ completed projects.",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Bursa",
        "addressCountry": "Turkey",
        "addressRegion": "Bursa Province"
      }
    }
    </script>

    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "Kenenisa Kero Portfolio",
      "alternateName": "Kenenis Portfolio",
      "url": "https://kenenis.com",
      "description": "Professional portfolio of Kenenisa Kero (Kenenis), a Full Stack Developer specializing in React, Node.js, and UI/UX Design",
      "inLanguage": "en-US",
      "isAccessibleForFree": true,
      "creator": {
        "@type": "Person",
        "name": "Kenenisa Kero"
      },
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://kenenis.com/?search={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "ProfessionalService",
      "name": "Kenenisa Kero - Full Stack Development Services",
      "image": "https://kenenis.com/og-image.svg",
      "description": "Professional full stack web development services including React.js, Node.js, TypeScript development, and UI/UX design.",
      "provider": {
        "@type": "Person",
        "name": "Kenenisa Kero",
        "alternateName": "Kenenis"
      },
      "areaServed": "Worldwide",
      "serviceType": [
        "Web Development",
        "Full Stack Development",
        "Frontend Development",
        "Backend Development",
        "UI/UX Design",
        "React Development",
        "Node.js Development"
      ],
      "url": "https://kenenis.com",
      "telephone": "+251-XXX-XXXX",
      "email": "<EMAIL>"
    }
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
