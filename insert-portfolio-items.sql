-- Insert sample portfolio items
-- Run this in Supabase SQL Editor to populate portfolio_items table

INSERT INTO portfolio_items (title, description, category, image_url, technologies, live_url, github_url, featured) VALUES
(
    'Mobile App Design',
    'Modern iOS & Android app with stunning UI/UX design and seamless user experience',
    'Mobile App',
    'https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-01.jpg',
    ARRAY['React Native', 'TypeScript', 'Figma', 'Firebase'],
    'https://example.com/mobile-app',
    'https://github.com/example/mobile-app',
    true
),
(
    'Website Redesign',
    'Complete website overhaul with modern design principles and responsive layout',
    'Web Design',
    'https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-02.jpg',
    ARRAY['React', 'Tailwind CSS', 'Framer Motion', 'Next.js'],
    'https://example.com/website-redesign',
    'https://github.com/example/website-redesign',
    true
),
(
    'Analytics Dashboard',
    'Real-time analytics dashboard with interactive data visualization and reporting',
    'Dashboard',
    'https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-03.jpg',
    ARRAY['Vue.js', 'Chart.js', 'Node.js', 'PostgreSQL'],
    'https://example.com/analytics-dashboard',
    'https://github.com/example/analytics-dashboard',
    false
),
(
    'SaaS Landing Page',
    'High-converting landing page for SaaS product with modern animations',
    'Landing Page',
    'https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-04.jpg',
    ARRAY['Next.js', 'GSAP', 'Stripe', 'Tailwind CSS'],
    'https://example.com/saas-landing',
    'https://github.com/example/saas-landing',
    true
),
(
    'E-commerce Mobile App',
    'Feature-rich e-commerce mobile application with seamless shopping experience',
    'Mobile App',
    'https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-05.jpg',
    ARRAY['Flutter', 'Firebase', 'Stripe', 'REST API'],
    'https://example.com/ecommerce-app',
    'https://github.com/example/ecommerce-app',
    false
),
(
    'Corporate Website',
    'Professional corporate website with content management system',
    'Web Design',
    'https://rainbowit.net/html/inbio/assets/images/portfolio/portfolio-06.jpg',
    ARRAY['WordPress', 'PHP', 'MySQL', 'Bootstrap'],
    'https://example.com/corporate-site',
    'https://github.com/example/corporate-site',
    false
),
(
    'Task Management App',
    'Collaborative task management application with real-time updates',
    'Web Application',
    'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop&q=80',
    ARRAY['React', 'Node.js', 'Socket.io', 'MongoDB'],
    'https://example.com/task-manager',
    'https://github.com/example/task-manager',
    false
),
(
    'Crypto Trading Dashboard',
    'Real-time cryptocurrency trading dashboard with advanced charting',
    'Dashboard',
    'https://images.unsplash.com/photo-1642790106117-e829e14a795f?w=600&h=400&fit=crop&q=80',
    ARRAY['React', 'D3.js', 'WebSocket', 'Express.js'],
    'https://example.com/crypto-dashboard',
    'https://github.com/example/crypto-dashboard',
    true
);

-- Success message
SELECT 'Portfolio items inserted successfully!' as message;
