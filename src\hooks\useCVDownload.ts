import { useQuery } from "@tanstack/react-query";
import { supabase } from "../services/supabase/client";

export const useCVDownload = () => {
  const {
    data: cvSettings,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["cv-settings"],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("site_settings")
          .select("value")
          .eq("key", "cv")
          .single();

        if (error || !data) {
          // Return default settings if no CV settings found
          return {
            cvFileUrl: "",
            cvFileName: "CV.pdf",
            enableDownload: false,
          };
        }

        return data.value;
      } catch (error) {
        console.warn("Failed to fetch CV settings:", error);
        return {
          cvFileUrl: "",
          cvFileName: "CV.pdf",
          enableDownload: false,
        };
      }
    },
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const downloadCV = () => {
    if (!cvSettings?.cvFileUrl || !cvSettings?.enableDownload) {
      console.warn("CV download not configured or disabled");
      return;
    }

    // Create a temporary link element to trigger download
    const link = document.createElement("a");
    link.href = cvSettings.cvFileUrl;
    link.download = cvSettings.cvFileName || "CV.pdf";
    link.target = "_blank";
    
    // Append to body, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return {
    cvSettings: cvSettings || {
      cvFileUrl: "",
      cvFileName: "CV.pdf",
      enableDownload: false,
    },
    isLoading,
    error,
    downloadCV,
    isDownloadEnabled: cvSettings?.enableDownload && cvSettings?.cvFileUrl,
  };
};
