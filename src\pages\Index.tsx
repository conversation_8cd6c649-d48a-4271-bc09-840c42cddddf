import React from "react";
import Header from "../components/layout/Header";
import Sidebar from "../components/layout/Sidebar";
import Hero from "../components/portfolio/Hero";
import About from "../components/portfolio/About";
import Resume from "../components/portfolio/Resume";
import Skills from "../components/portfolio/Skills";
import Portfolio from "../components/portfolio/Portfolio";
import BlogSection from "../components/portfolio/BlogSection";
import Testimonials from "../components/common/Testimonials";
import Contact from "../components/forms/Contact";
import Footer from "../components/layout/Footer";

const Index = () => {
  return (
    <div className="min-h-screen flex flex-col lg:flex-row relative">
      {/* Left Sidebar - Hidden on mobile, fixed on desktop */}
      <div className="hidden lg:block">
        <Sidebar />
      </div>

      {/* Main Content */}
      <div className="w-full lg:ml-[380px]">
        {/* Header */}
        <Header />

        {/* Sections */}
        <main>
          <Hero />
          <About />
          <Resume />
          <Skills />
          <Portfolio />
          <BlogSection />
          <Testimonials />
          <Contact />
          <Footer />
        </main>
      </div>
    </div>
  );
};

export default Index;
