// Language utility for handling English and Turkish translations
export type Language = 'en' | 'tr';

export interface LanguageConfig {
  code: Language;
  name: string;
  nativeName: string;
}

export const SUPPORTED_LANGUAGES: LanguageConfig[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English'
  },
  {
    code: 'tr',
    name: 'Turkish',
    nativeName: 'Türkçe'
  }
];

export const DEFAULT_LANGUAGE: Language = 'en';

// Get language configuration by code
export const getLanguageConfig = (code: Language): LanguageConfig => {
  return SUPPORTED_LANGUAGES.find(lang => lang.code === code) || SUPPORTED_LANGUAGES[0];
};

// Validate if a language code is supported
export const isValidLanguage = (code: string): code is Language => {
  return SUPPORTED_LANGUAGES.some(lang => lang.code === code);
};

// Get browser language preference (fallback to English if not supported)
export const getBrowserLanguage = (): Language => {
  if (typeof window === 'undefined') return DEFAULT_LANGUAGE;
  
  const browserLang = navigator.language.split('-')[0] as Language;
  return isValidLanguage(browserLang) ? browserLang : DEFAULT_LANGUAGE;
};

// Basic translations for common UI elements
export const translations = {
  en: {
    // Common
    save: 'Save',
    cancel: 'Cancel',
    edit: 'Edit',
    delete: 'Delete',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    
    // Settings
    settings: 'Settings',
    general: 'General',
    language: 'Language',
    siteName: 'Site Name',
    siteDescription: 'Site Description',
    contactEmail: 'Contact Email',
    maintenanceMode: 'Maintenance Mode',
    saveChanges: 'Save Changes',
    
    // Navigation
    home: 'Home',
    about: 'About',
    portfolio: 'Portfolio',
    contact: 'Contact',
    
    // Messages
    settingsUpdated: 'Settings updated successfully!',
    settingsError: 'Failed to update settings',
  },
  tr: {
    // Common
    save: 'Kaydet',
    cancel: 'İptal',
    edit: 'Düzenle',
    delete: 'Sil',
    loading: 'Yükleniyor...',
    error: 'Hata',
    success: 'Başarılı',
    
    // Settings
    settings: 'Ayarlar',
    general: 'Genel',
    language: 'Dil',
    siteName: 'Site Adı',
    siteDescription: 'Site Açıklaması',
    contactEmail: 'İletişim E-postası',
    maintenanceMode: 'Bakım Modu',
    saveChanges: 'Değişiklikleri Kaydet',
    
    // Navigation
    home: 'Ana Sayfa',
    about: 'Hakkında',
    portfolio: 'Portföy',
    contact: 'İletişim',
    
    // Messages
    settingsUpdated: 'Ayarlar başarıyla güncellendi!',
    settingsError: 'Ayarlar güncellenirken hata oluştu',
  }
};

// Get translation for a key
export const getTranslation = (key: keyof typeof translations.en, language: Language = DEFAULT_LANGUAGE): string => {
  return translations[language]?.[key] || translations[DEFAULT_LANGUAGE][key] || key;
};

// Translation hook-like function
export const useTranslation = (language: Language = DEFAULT_LANGUAGE) => {
  return {
    t: (key: keyof typeof translations.en) => getTranslation(key, language),
    language,
    isRTL: false, // Neither English nor Turkish are RTL languages
  };
};
