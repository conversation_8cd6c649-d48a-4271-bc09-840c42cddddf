import { useState, useEffect } from "react";
import { supabase } from "../../../services/supabase/client";

export const useDatabaseStatus = () => {
  const [isConnected, setIsConnected] = useState(true);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkConnection = async () => {
    try {
      // Simple query to test connection
      const { error } = await supabase
        .from("portfolio_content")
        .select("id")
        .limit(1);
      
      if (error) {
        setIsConnected(false);
      } else {
        setIsConnected(true);
      }
    } catch (error) {
      setIsConnected(false);
    }
    setLastChecked(new Date());
  };

  useEffect(() => {
    // Check connection on mount
    checkConnection();

    // Check connection every 30 seconds
    const interval = setInterval(checkConnection, 30000);

    return () => clearInterval(interval);
  }, []);

  return {
    isConnected,
    lastChecked,
    checkConnection,
  };
};
