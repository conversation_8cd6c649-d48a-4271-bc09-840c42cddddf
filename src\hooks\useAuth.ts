import { useState, useEffect } from "react";
import { User } from "@supabase/supabase-js";
import { supabase } from "../services/supabase/client";
import { toast } from "sonner";

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(() => {
    // Initialize from localStorage if available
    const cached = localStorage.getItem('isAdmin');
    return cached === 'true';
  });

  useEffect(() => {
    // Get initial session
    const getSession = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        setUser(session?.user ?? null);

        if (session?.user) {
          // Check admin status in parallel, don't wait for it
          checkAdminStatus(session.user.id);
        } else {
          // Clear cache if no user
          localStorage.removeItem('isAdmin');
        }

        setLoading(false);
      } catch (error) {
        console.error("Error getting session:", error);
        setLoading(false);
      }
    };

    getSession();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setUser(session?.user ?? null);

      if (session?.user) {
        // Check admin status in parallel for auth changes too
        checkAdminStatus(session.user.id);
      } else {
        setIsAdmin(false);
        localStorage.removeItem('isAdmin');
      }

      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const checkAdminStatus = async (userId: string) => {
    try {

      // Check cache first
      const cachedStatus = localStorage.getItem(`admin_${userId}`);
      const cacheTime = localStorage.getItem(`admin_${userId}_time`);
      const now = Date.now();

      // Use cache if it's less than 5 minutes old
      if (cachedStatus && cacheTime && (now - parseInt(cacheTime)) < 5 * 60 * 1000) {
        const isAdminUser = cachedStatus === 'true';
        setIsAdmin(isAdminUser);
        localStorage.setItem('isAdmin', isAdminUser.toString());
        return;
      }

      // Add timeout to the database query
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Admin check timeout')), 3000)
      );

      const queryPromise = supabase
        .from("admin_users")
        .select("role, is_active")
        .eq("id", userId)
        .single();

      const { data, error } = await Promise.race([queryPromise, timeoutPromise]) as any;

      if (error || !data) {
        setIsAdmin(false);
        localStorage.setItem('isAdmin', 'false');
        localStorage.setItem(`admin_${userId}`, 'false');
        localStorage.setItem(`admin_${userId}_time`, now.toString());
        return;
      }

      const isAdminUser = data.is_active && data.role === "admin";
      setIsAdmin(isAdminUser);

      // Cache the result
      localStorage.setItem('isAdmin', isAdminUser.toString());
      localStorage.setItem(`admin_${userId}`, isAdminUser.toString());
      localStorage.setItem(`admin_${userId}_time`, now.toString());
    } catch (error) {
      // On error, use cached value if available, otherwise assume not admin
      const cachedStatus = localStorage.getItem('isAdmin');
      const fallbackAdmin = cachedStatus === 'true';
      setIsAdmin(fallbackAdmin);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast.error(error.message);
        setLoading(false);
        return { success: false, error };
      }

      if (data.user) {
        // Check admin status properly
        await checkAdminStatus(data.user.id);
        toast.success("Login successful!");
        setLoading(false);
        return { success: true, user: data.user };
      }

      setLoading(false);
      return { success: false, error: new Error("No user returned") };
    } catch (error) {
      console.error("Login error:", error);
      toast.error("Login failed - please try again");
      setLoading(false);
      return { success: false, error };
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();

      if (error) {
        toast.error(error.message);
        return { success: false, error };
      }

      setIsAdmin(false);

      // Clear admin cache
      localStorage.removeItem('isAdmin');
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('admin_')) {
          localStorage.removeItem(key);
        }
      });

      toast.success("Logged out successfully!");
      return { success: true };
    } catch (error) {
      toast.error("An unexpected error occurred");
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) {
        toast.error(error.message);
        return { success: false, error };
      }

      if (data.user) {
        // Create admin user record
        const { error: adminError } = await supabase
          .from("admin_users")
          .insert({
            id: data.user.id,
            email: data.user.email!,
            role: "admin",
            is_active: true,
          });

        if (adminError) {
          console.error("Error creating admin user:", adminError);
        }

        toast.success("Admin account created successfully!");
        return { success: true, user: data.user };
      }

      return { success: false, error: new Error("No user returned") };
    } catch (error) {
      toast.error("An unexpected error occurred");
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  return {
    user,
    loading,
    isAdmin,
    signIn,
    signOut,
    signUp,
  };
};
