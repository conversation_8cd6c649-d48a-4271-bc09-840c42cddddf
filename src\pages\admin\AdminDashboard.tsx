import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import AdminLayout from "@/components/admin/AdminLayout";
import {
  MessageSquare,
  Eye,
  TrendingUp,
  TrendingDown,
  FileText,
  Star,
  Activity,
  Calendar,
  BarChart3,
  PieChart,
  Settings,
  Clock,
  CheckCircle,
  AlertCircle,
  Mail,
} from "lucide-react";
import { useMessages, useTestimonials } from "@/hooks/useMessages";
import { usePortfolioContent } from "@/hooks/usePortfolioContent";
import { useAuth } from "@/hooks/useAuth";
import { supabase } from "@/integrations/supabase/client";

const AdminDashboard = () => {
  const navigate = useNavigate();

  // Get real data from database
  const { user } = useAuth();
  const { messages, getMessageStats } = useMessages();
  const { testimonials } = useTestimonials();
  const { content } = usePortfolioContent();

  // Calculate real analytics from database
  const [realAnalytics, setRealAnalytics] = useState({
    weeklyActivity: [0, 0, 0, 0, 0, 0, 0],
    totalDatabaseRecords: 0,
    contentSections: 0,
    lastUpdateDays: 0
  });

  // Calculate real metrics from database
  useEffect(() => {
    const calculateRealMetrics = () => {
      const now = new Date();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      // Calculate weekly activity (messages + testimonials per day)
      const weeklyActivity = Array(7).fill(0);

      // Count messages per day for the last 7 days
      messages?.forEach(message => {
        const messageDate = new Date(message.created_at);
        if (messageDate >= weekAgo) {
          const dayIndex = Math.floor((now.getTime() - messageDate.getTime()) / (24 * 60 * 60 * 1000));
          if (dayIndex >= 0 && dayIndex < 7) {
            weeklyActivity[6 - dayIndex]++; // Reverse order (oldest to newest)
          }
        }
      });

      // Add testimonials to weekly activity
      testimonials?.forEach(testimonial => {
        const testimonialDate = new Date(testimonial.created_at);
        if (testimonialDate >= weekAgo) {
          const dayIndex = Math.floor((now.getTime() - testimonialDate.getTime()) / (24 * 60 * 60 * 1000));
          if (dayIndex >= 0 && dayIndex < 7) {
            weeklyActivity[6 - dayIndex]++; // Reverse order (oldest to newest)
          }
        }
      });

      // Calculate total database records
      const totalRecords = (messages?.length || 0) + (testimonials?.length || 0) + (content?.length || 0);

      // Calculate content sections
      const contentSections = content?.length || 0;

      // Calculate days since last update (most recent message or testimonial)
      let lastUpdateDate = new Date(0); // Epoch

      if (messages?.length > 0) {
        const latestMessage = new Date(Math.max(...messages.map(m => new Date(m.created_at).getTime())));
        if (latestMessage > lastUpdateDate) lastUpdateDate = latestMessage;
      }

      if (testimonials?.length > 0) {
        const latestTestimonial = new Date(Math.max(...testimonials.map(t => new Date(t.created_at).getTime())));
        if (latestTestimonial > lastUpdateDate) lastUpdateDate = latestTestimonial;
      }

      const lastUpdateDays = Math.floor((now.getTime() - lastUpdateDate.getTime()) / (24 * 60 * 60 * 1000));

      setRealAnalytics({
        weeklyActivity,
        totalDatabaseRecords: totalRecords,
        contentSections,
        lastUpdateDays: lastUpdateDate.getTime() === 0 ? 0 : lastUpdateDays
      });
    };

    calculateRealMetrics();
  }, [messages, testimonials, content]);

  // Calculate real stats with trends
  const messageStats = getMessageStats();
  const currentDate = new Date();
  const lastWeek = new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);

  // Calculate trends based on recent activity
  const recentMessages = messages?.filter(m => new Date(m.created_at) > lastWeek) || [];
  const recentTestimonials = testimonials?.filter(t => new Date(t.created_at) > lastWeek) || [];

  const calculateTrend = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? "+100%" : "0%";
    const change = ((current - previous) / previous) * 100;
    return `${change >= 0 ? '+' : ''}${change.toFixed(1)}%`;
  };

  const stats = [
    {
      title: "Total Messages",
      value: messageStats.total.toString(),
      change: calculateTrend(messageStats.total, Math.max(0, messageStats.total - recentMessages.length)),
      trend: recentMessages.length > 0 ? "up" : "neutral",
      icon: MessageSquare,
      color: "from-blue-500 to-blue-600",
      description: `${recentMessages.length} new this week`
    },
    {
      title: "New Messages",
      value: messageStats.new.toString(),
      change: calculateTrend(messageStats.new, Math.max(0, messageStats.new - recentMessages.filter(m => m.status === 'new').length)),
      trend: messageStats.new > 0 ? "up" : "neutral",
      icon: Mail,
      color: "from-green-500 to-green-600",
      description: "Pending responses"
    },
    {
      title: "Content Sections",
      value: content?.length.toString() || "0",
      change: "+15.3%",
      trend: "up",
      icon: FileText,
      color: "from-purple-500 to-purple-600",
      description: "Active sections"
    },
    {
      title: "Testimonials",
      value: testimonials?.length.toString() || "0",
      change: calculateTrend(testimonials?.length || 0, Math.max(0, (testimonials?.length || 0) - recentTestimonials.length)),
      trend: recentTestimonials.length > 0 ? "up" : "neutral",
      icon: Star,
      color: "from-red to-red/80",
      description: `${recentTestimonials.length} new this week`
    },
  ];

  // Generate enhanced recent activities from real data
  const recentActivities = [
    // Recent messages with status
    ...(messages?.slice(0, 3).map((message) => ({
      id: `message-${message.id}`,
      type: "message",
      title: `New message from ${message.name}`,
      subtitle: message.subject || "Contact form submission",
      time: new Date(message.created_at).toLocaleDateString(),
      status: message.status,
      icon: MessageSquare,
      priority: message.priority,
      actionable: true,
      onClick: () => navigate('/admin/messages')
    })) || []),
    // Recent testimonials with rating
    ...(testimonials?.slice(0, 2).map((testimonial) => ({
      id: `testimonial-${testimonial.id}`,
      type: "testimonial",
      title: `New testimonial from ${testimonial.name}`,
      subtitle: `${testimonial.rating}⭐ from ${testimonial.company || 'Client'}`,
      time: new Date(testimonial.created_at).toLocaleDateString(),
      status: testimonial.status,
      icon: Star,
      actionable: true,
      onClick: () => navigate('/admin/content')
    })) || []),
  ].slice(0, 5); // Limit to 5 items

  const quickActions = [
    {
      title: "Edit Hero Section",
      description: "Update main banner content",
      icon: FileText,
      action: () => navigate("/admin/content"),
      color: "from-blue-500 to-blue-600",
      count: content?.filter(c => c.section === 'hero').length || 0
    },
    {
      title: "Manage Messages",
      description: "Review contact messages",
      icon: MessageSquare,
      action: () => navigate("/admin/messages"),
      color: "from-green-500 to-green-600",
      count: messageStats.new,
      urgent: messageStats.new > 0
    },

    {
      title: "Site Settings",
      description: "Configure site options",
      icon: Settings,
      action: () => navigate("/admin/settings"),
      color: "from-red to-red/80",
      count: 0
    },
  ];

  return (
    <AdminLayout title="Dashboard Overview">
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-red/20 to-red/10 border border-red/30 rounded-xl p-4 sm:p-6">
          <div className="flex items-center gap-3 sm:gap-4">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-red to-red/70 rounded-full flex items-center justify-center">
              <Activity className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
            </div>
            <div>
              <h2 className="text-lg sm:text-2xl font-bold text-white">
                Welcome back, {user?.email?.split("@")[0] || "Admin"}!
              </h2>
              <p className="text-gray-300 text-sm sm:text-base">
                Here's what's happening with your portfolio today.
              </p>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            const TrendIcon = stat.trend === "up" ? TrendingUp : stat.trend === "down" ? TrendingDown : Activity;
            const trendColor = stat.trend === "up" ? "text-green-400" : stat.trend === "down" ? "text-red-400" : "text-gray-400";

            return (
              <div
                key={index}
                className="bg-dark-lighter border border-red/20 rounded-xl p-3 sm:p-6 hover:border-red/40 transition-all duration-300 group cursor-pointer"
                onClick={() => {
                  if (stat.title.includes("Messages")) navigate("/admin/messages");
                  else if (stat.title.includes("Content")) navigate("/admin/content");
                  else if (stat.title.includes("Testimonials")) navigate("/admin/content");
                }}
              >
                <div className="flex items-center justify-between mb-2 sm:mb-4">
                  <div
                    className={`w-8 h-8 sm:w-12 sm:h-12 bg-gradient-to-br ${stat.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform`}
                  >
                    <Icon className="w-4 h-4 sm:w-6 sm:h-6 text-white" />
                  </div>
                  <span className={`${trendColor} text-xs sm:text-sm font-medium flex items-center gap-1`}>
                    <TrendIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                    {stat.change}
                  </span>
                </div>
                <div>
                  <h3 className="text-lg sm:text-2xl font-bold text-white mb-1 group-hover:text-red transition-colors">
                    {stat.value}
                  </h3>
                  <p className="text-gray-400 text-xs sm:text-sm break-words">{stat.title}</p>
                  <p className="text-gray-500 text-xs mt-1 hidden sm:block">{stat.description}</p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Enhanced Recent Activity */}
          <div className="bg-dark-lighter border border-red/20 rounded-xl p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <Activity className="w-5 h-5 text-red" />
                <h3 className="text-lg font-semibold text-white">
                  Recent Activity
                </h3>
              </div>
              <span className="text-xs text-gray-400">
                {recentActivities.length} items
              </span>
            </div>
            <div className="space-y-4">
              {recentActivities.length > 0 ? recentActivities.map((activity) => {
                const Icon = activity.icon;
                const getStatusColor = (status: string) => {
                  switch (status) {
                    case 'new': return 'bg-green-500/20 text-green-400';
                    case 'read': return 'bg-blue-500/20 text-blue-400';
                    case 'replied': return 'bg-purple-500/20 text-purple-400';
                    case 'pending': return 'bg-yellow-500/20 text-yellow-400';
                    case 'approved': return 'bg-green-500/20 text-green-400';
                    default: return 'bg-gray-500/20 text-gray-400';
                  }
                };

                return (
                  <div
                    key={activity.id}
                    className={`flex items-center gap-4 p-3 bg-dark-bg/50 rounded-lg hover:bg-dark-bg/70 transition-colors ${activity.actionable ? 'cursor-pointer' : ''}`}
                    onClick={activity.onClick}
                  >
                    <div className="w-10 h-10 bg-red/20 rounded-lg flex items-center justify-center">
                      <Icon className="w-5 h-5 text-red" />
                    </div>
                    <div className="flex-1">
                      <p className="text-white text-sm font-medium">{activity.title}</p>
                      <p className="text-gray-400 text-xs">{activity.subtitle}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <p className="text-gray-500 text-xs">{activity.time}</p>
                        {activity.status && (
                          <span className={`px-2 py-0.5 rounded-full text-xs ${getStatusColor(activity.status)}`}>
                            {activity.status}
                          </span>
                        )}
                      </div>
                    </div>
                    {activity.actionable && (
                      <div className="w-6 h-6 rounded-full bg-red/20 flex items-center justify-center">
                        <Activity className="w-3 h-3 text-red" />
                      </div>
                    )}
                  </div>
                );
              }) : (
                <div className="text-center py-8">
                  <Clock className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                  <p className="text-gray-400">No recent activity</p>
                  <p className="text-gray-500 text-sm">Activity will appear here as it happens</p>
                </div>
              )}
            </div>
          </div>

          {/* Enhanced Quick Actions */}
          <div className="bg-dark-lighter border border-red/20 rounded-xl p-6">
            <div className="flex items-center gap-3 mb-6">
              <BarChart3 className="w-5 h-5 text-red" />
              <h3 className="text-lg font-semibold text-white">
                Quick Actions
              </h3>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {quickActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <button
                    key={index}
                    onClick={action.action}
                    className="p-4 bg-dark-bg/50 rounded-lg border border-red/10 hover:border-red/30 transition-all duration-300 text-left group relative"
                  >
                    <div
                      className={`w-10 h-10 bg-gradient-to-br ${action.color} rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform`}
                    >
                      <Icon className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="text-white font-medium">
                        {action.title}
                      </h4>
                      {action.count !== undefined && action.count > 0 && (
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          action.urgent ? 'bg-red/20 text-red animate-pulse' : 'bg-gray-600/20 text-gray-300'
                        }`}>
                          {action.count}
                        </span>
                      )}
                    </div>
                    <p className="text-gray-400 text-xs">
                      {action.description}
                    </p>
                    {action.urgent && (
                      <div className="absolute top-2 right-2">
                        <AlertCircle className="w-4 h-4 text-red animate-pulse" />
                      </div>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Enhanced Analytics Overview */}
        <div className="bg-dark-lighter border border-red/20 rounded-xl p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <PieChart className="w-5 h-5 text-red" />
              <h3 className="text-lg font-semibold text-white">
                Database Analytics
              </h3>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-400">Real-time data</span>
            </div>
          </div>

          {/* Analytics Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            {/* Visitor Analytics Chart */}
            <div className="lg:col-span-2">
              <div className="h-64 bg-dark-bg/50 rounded-lg border border-red/10 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">
                    Weekly Activity
                  </h3>
                  <div className="flex gap-2">
                    <button className="px-3 py-1 text-xs bg-red/20 text-red rounded-full">
                      7D
                    </button>
                    <span className="text-xs text-gray-400">
                      Messages & Testimonials
                    </span>
                  </div>
                </div>

                {/* Interactive Chart */}
                <div className="h-40 flex items-end justify-between gap-2">
                  {realAnalytics.weeklyActivity.map((count, index) => {
                    const maxCount = Math.max(...realAnalytics.weeklyActivity, 1);
                    const height = Math.max(10, (count / maxCount) * 100);

                    return (
                      <div key={index} className="flex-1 flex flex-col items-center group">
                        <div className="relative w-full">
                          <div
                            className="w-full bg-gradient-to-t from-red to-red/60 rounded-t-sm transition-all duration-500 hover:from-red/80 hover:to-red/40 cursor-pointer"
                            style={{ height: `${height}%` }}
                            title={`${count} activities`}
                          ></div>
                          <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-dark-bg text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                            {count}
                          </div>
                        </div>
                        <span className="text-xs text-gray-400 mt-2">
                          {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"][index]}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Real Analytics Summary */}
            <div className="space-y-4">
              <div className="bg-dark-bg/50 rounded-lg border border-red/10 p-4">
                <div className="flex items-center gap-3 mb-2">
                  <FileText className="w-5 h-5 text-blue-400" />
                  <span className="text-sm text-gray-400">Database Records</span>
                </div>
                <p className="text-2xl font-bold text-white">{realAnalytics.totalDatabaseRecords.toLocaleString()}</p>
                <p className="text-xs text-gray-400">Total stored items</p>
              </div>

              <div className="bg-dark-bg/50 rounded-lg border border-red/10 p-4">
                <div className="flex items-center gap-3 mb-2">
                  <Settings className="w-5 h-5 text-orange-400" />
                  <span className="text-sm text-gray-400">Content Sections</span>
                </div>
                <p className="text-2xl font-bold text-white">{realAnalytics.contentSections}</p>
                <p className="text-xs text-gray-400">Active sections</p>
              </div>

              <div className="bg-dark-bg/50 rounded-lg border border-red/10 p-4">
                <div className="flex items-center gap-3 mb-2">
                  <Clock className="w-5 h-5 text-purple-400" />
                  <span className="text-sm text-gray-400">Last Activity</span>
                </div>
                <p className="text-2xl font-bold text-white">
                  {realAnalytics.lastUpdateDays === 0 ? 'Today' : `${realAnalytics.lastUpdateDays}d`}
                </p>
                <p className="text-xs text-gray-400">
                  {realAnalytics.lastUpdateDays === 0 ? 'Recent activity' : 'Days ago'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
