import { Toaster } from "./components/ui/toaster";
import { Toaster as Sonner } from "./components/ui/sonner";
import { TooltipProvider } from "./components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { ThemeProvider } from "./app/providers/ThemeContext";
import Index from "./pages/Index";
import Blog from "./pages/Blog";
import BlogPost from "./pages/BlogPost";
import NotFound from "./pages/NotFound";
import AdminLogin from "./features/admin/pages/AdminLogin";
import AdminDashboard from "./features/admin/pages/AdminDashboard";
import AdminContent from "./features/admin/pages/AdminContent";
import AdminBlog from "./features/admin/pages/AdminBlog";
import AdminBlogCreate from "./features/admin/pages/AdminBlogCreate";
import AdminBlogEdit from "./features/admin/pages/AdminBlogEdit";
import AdminBlogComments from "./features/admin/pages/AdminBlogComments";
import AdminMessages from "./features/admin/pages/AdminMessages";
import AdminSettings from "./features/admin/pages/AdminSettings";
import AdminAuthGuard from "./features/admin/components/AdminAuthGuard";
import RealtimeNotification from "./components/common/RealtimeNotification";

import OfflineIndicator from "./components/common/OfflineIndicator";
import { usePWA } from "./hooks/usePWA";

const queryClient = new QueryClient();

// PWA wrapper component to ensure hooks are called within provider tree
const PWAWrapper = ({ children }: { children: React.ReactNode }) => {
  // Initialize PWA functionality
  usePWA();
  return <>{children}</>;
};

const App = () => {
  return (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <TooltipProvider>
            <PWAWrapper>
              <Toaster />
              <Sonner />
              <RealtimeNotification />
              <OfflineIndicator />
              <BrowserRouter>
              <Routes>
                <Route path="/" element={<Index />} />

                {/* Blog Routes */}
                <Route path="/blog" element={<Blog />} />
                <Route path="/blog/:slug" element={<BlogPost />} />

                {/* Admin Routes */}
                <Route path="/admin" element={<AdminLogin />} />
                <Route path="/admin/dashboard" element={<AdminAuthGuard><AdminDashboard /></AdminAuthGuard>} />
                <Route path="/admin/content" element={<AdminAuthGuard><AdminContent /></AdminAuthGuard>} />
                <Route path="/admin/blog" element={<AdminAuthGuard><AdminBlog /></AdminAuthGuard>} />
                <Route path="/admin/blog/create" element={<AdminAuthGuard><AdminBlogCreate /></AdminAuthGuard>} />
                <Route path="/admin/blog/edit/:id" element={<AdminAuthGuard><AdminBlogEdit /></AdminAuthGuard>} />
                <Route path="/admin/blog/comments" element={<AdminAuthGuard><AdminBlogComments /></AdminAuthGuard>} />
                <Route path="/admin/messages" element={<AdminAuthGuard><AdminMessages /></AdminAuthGuard>} />
                <Route path="/admin/settings" element={<AdminAuthGuard><AdminSettings /></AdminAuthGuard>} />

                {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
          </PWAWrapper>
        </TooltipProvider>
      </ThemeProvider>
    </QueryClientProvider>
    </HelmetProvider>
  );
};

export default App;
