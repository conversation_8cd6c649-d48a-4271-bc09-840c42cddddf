import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "../services/supabase/client";
import { toast } from "sonner";
import { useEffect } from "react";

export interface PortfolioContent {
  id: string;
  section: string;
  content: any;
  created_at: string;
  updated_at: string;
}

export const usePortfolioContent = () => {
  const queryClient = useQueryClient();

  // Fetch all content
  const {
    data: content,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["portfolio-content"],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("portfolio_content")
          .select("*")
          .order("section");

        if (error) throw error;
        return data as PortfolioContent[];
      } catch (error) {
        console.warn("Database connection failed, using fallback data:", error);
        // Return empty array to trigger fallback data usage
        return [];
      }
    },
    retry: false, // Don't retry on network errors
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Get content by section
  const getContentBySection = (section: string) => {
    return content?.find((item) => item.section === section)?.content;
  };

  // Update content mutation
  const updateContentMutation = useMutation({
    mutationFn: async ({
      section,
      content,
    }: {
      section: string;
      content: any;
    }) => {
      try {

        // Use proper upsert with conflict resolution on the section field
        const { data, error } = await supabase
          .from("portfolio_content")
          .upsert(
            {
              section,
              content,
              updated_at: new Date().toISOString(),
            },
            {
              onConflict: "section", // Specify the conflict column
              ignoreDuplicates: false, // We want to update, not ignore
            }
          )
          .select()
          .single();

        if (error) {
          console.error("Upsert error:", error);
          throw error;
        }

        return data;
      } catch (error) {
        console.warn("Database update failed:", error);

        // For network errors
        if (error.message?.includes("Failed to fetch")) {
          toast.warning("Database unavailable - changes saved locally only");
        } else {
          toast.error(`Failed to update content: ${error.message}`);
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["portfolio-content"] });
      toast.success("Content updated successfully!");
    },
    onError: (error) => {
      console.error("Error updating content:", error);
      // Error handling is done in mutationFn
    },
  });

  // Real-time subscription for portfolio content (only if database is available)
  useEffect(() => {
    // Skip real-time subscription if there's a connection error
    if (error) return;

    try {
      const channel = supabase
        .channel("portfolio_content_changes")
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "portfolio_content",
          },
          (payload) => {
            queryClient.invalidateQueries({ queryKey: ["portfolio-content"] });
          }
        )
        .subscribe();

      return () => {
        supabase.removeChannel(channel);
      };
    } catch (error) {
      console.warn("Real-time subscription failed:", error);
    }
  }, [queryClient, error]);

  return {
    content,
    isLoading,
    error,
    getContentBySection,
    updateContent: updateContentMutation.mutate,
    isUpdating: updateContentMutation.isPending,
  };
};

export interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  category: string;
  image_url: string | null;
  technologies: string[];
  live_url: string | null;
  github_url: string | null;
  featured: boolean;
  created_at: string;
  updated_at: string;
}

export const usePortfolioItems = () => {
  const queryClient = useQueryClient();

  // Fetch portfolio items
  const {
    data: portfolioItems,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["portfolio-items"],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("portfolio_items")
          .select("*")
          .order("created_at", { ascending: false });

        if (error) {
          console.error("Error fetching portfolio items:", error);
          throw error;
        }

        return data as PortfolioItem[];
      } catch (error) {
        console.warn("Database connection failed for portfolio items, using fallback:", error);
        // Return empty array to trigger fallback data usage
        return [];
      }
    },
    retry: false, // Don't retry on network errors
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Get unique categories from portfolio items
  const getCategories = () => {
    if (!portfolioItems) return ["All"];
    const categories = Array.from(new Set(portfolioItems.map(item => item.category)));
    return ["All", ...categories];
  };

  // Filter items by category
  const getItemsByCategory = (category: string) => {
    if (!portfolioItems) return [];
    if (category === "All") return portfolioItems;
    return portfolioItems.filter(item => item.category === category);
  };

  // Get featured items
  const getFeaturedItems = () => {
    if (!portfolioItems) return [];
    return portfolioItems.filter(item => item.featured);
  };

  // Create portfolio item
  const createItemMutation = useMutation({
    mutationFn: async (item: Omit<PortfolioItem, "id" | "created_at" | "updated_at">) => {
      try {
        const { data, error } = await supabase
          .from("portfolio_items")
          .insert({
            ...item,
            technologies: item.technologies || [],
          })
          .select()
          .single();

        if (error) {
          console.error("Error creating portfolio item:", error);
          throw error;
        }
        return data;
      } catch (error) {
        console.warn("Database unavailable for creating portfolio item:", error);
        toast.warning("Database unavailable - item not saved");
        throw error;
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["portfolio-items"] });
      toast.success("Portfolio item created successfully!");
    },
    onError: (error) => {
      console.error("Error creating portfolio item:", error);
      if (!error.message?.includes("Failed to fetch")) {
        toast.error("Failed to create portfolio item");
      }
    },
  });

  // Update portfolio item
  const updateItemMutation = useMutation({
    mutationFn: async ({ id, ...updates }: Partial<PortfolioItem> & { id: string }) => {
      const { data, error } = await supabase
        .from("portfolio_items")
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .select()
        .single();

      if (error) {
        console.error("Error updating portfolio item:", error);
        throw error;
      }
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["portfolio-items"] });
      toast.success("Portfolio item updated successfully!");
    },
    onError: (error) => {
      console.error("Error updating portfolio item:", error);
      toast.error("Failed to update portfolio item");
    },
  });

  // Delete portfolio item
  const deleteItemMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from("portfolio_items")
        .delete()
        .eq("id", id);

      if (error) {
        console.error("Error deleting portfolio item:", error);
        throw error;
      }
      return id;
    },
    onSuccess: (id) => {
      queryClient.invalidateQueries({ queryKey: ["portfolio-items"] });
      toast.success("Portfolio item deleted successfully!");
    },
    onError: (error) => {
      console.error("Error deleting portfolio item:", error);
      toast.error("Failed to delete portfolio item");
    },
  });

  // Real-time subscription for portfolio items (only if database is available)
  useEffect(() => {
    // Skip real-time subscription if there's a connection error
    if (error) return;

    try {
      const channel = supabase
        .channel("portfolio_items_changes")
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "portfolio_items",
          },
          (payload) => {
            queryClient.invalidateQueries({ queryKey: ["portfolio-items"] });
          }
        )
        .subscribe();

      return () => {
        supabase.removeChannel(channel);
      };
    } catch (error) {
      console.warn("Real-time subscription failed for portfolio items:", error);
    }
  }, [queryClient, error]);

  return {
    portfolioItems,
    isLoading,
    error,
    getCategories,
    getItemsByCategory,
    getFeaturedItems,
    createItem: createItemMutation.mutate,
    updateItem: updateItemMutation.mutate,
    deleteItem: deleteItemMutation.mutate,
    isCreating: createItemMutation.isPending,
    isUpdating: updateItemMutation.isPending,
    isDeleting: deleteItemMutation.isPending,
  };
};
