-- Insert or update hero section content
INSERT INTO portfolio_content (section, content)
VALUES (
  'hero',
  '{
    "name": "<PERSON><PERSON><PERSON>",
    "profession": "Full Stack Developer",
    "welcomeText": "WELCOME TO MY WORLD",
    "description": "I''m a passionate full stack developer specializing in building exceptional digital experiences. Currently, I''m focused on building accessible, human-centered products.",
    "buttonText": "More About Me",
    "profileImage": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face&auto=format&q=80",
    "typingTexts": ["Full Stack Developer", "UI/UX Designer", "Web Developer"],
    "professions": ["Full Stack Developer", "UI/UX Designer", "Web Developer", "Software Engineer"]
  }'::jsonb
)
ON CONFLICT (section) 
DO UPDATE SET 
  content = EXCLUDED.content,
  updated_at = NOW();
