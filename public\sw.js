// Kenenis Dev Portfolio - Service Worker
// Version 1.0.0

const CACHE_NAME = 'kenenis-dev-portfolio-v1';
const STATIC_CACHE_NAME = 'kenenis-dev-static-v1';
const DYNAMIC_CACHE_NAME = 'kenenis-dev-dynamic-v1';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/favicon.svg',
  '/favicon-16x16.svg',
  '/favicon-32x32.svg',
  '/apple-touch-icon.svg',
  '/pwa-192x192.svg',
  '/pwa-512x512.svg',
  '/site.webmanifest',
  '/robots.txt'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME && cacheName !== DYNAMIC_CACHE_NAME) {
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-http(s) requests (chrome-extension, etc.)
  if (!url.protocol.startsWith('http')) {
    return;
  }

  // Skip cross-origin requests that we can't cache (except for specific allowed domains)
  const allowedDomains = ['supabase.co', 'fonts.googleapis.com', 'fonts.gstatic.com'];
  if (url.origin !== self.location.origin && !allowedDomains.some(domain => url.hostname.includes(domain))) {
    return;
  }

  // Handle navigation requests (HTML pages)
  if (request.mode === 'navigate') {
    event.respondWith(
      caches.match('/')
        .then((cachedResponse) => {
          if (cachedResponse) {
            return cachedResponse;
          }
          return fetch(request)
            .then((response) => {
              // Cache successful responses
              if (response.status === 200) {
                const responseClone = response.clone();
                caches.open(DYNAMIC_CACHE_NAME)
                  .then((cache) => {
                    cache.put(request, responseClone);
                  })
                  .catch((error) => {
                    console.warn('Failed to cache response:', error);
                  });
              }
              return response;
            })
            .catch(() => {
              // Return offline page if available
              return caches.match('/');
            });
        })
    );
    return;
  }

  // Handle static assets
  if (STATIC_ASSETS.includes(url.pathname)) {
    event.respondWith(
      caches.match(request)
        .then((cachedResponse) => {
          return cachedResponse || fetch(request);
        })
    );
    return;
  }

  // Handle API requests and other resources
  event.respondWith(
    caches.match(request)
      .then((cachedResponse) => {
        if (cachedResponse) {
          // Serve from cache and update in background
          fetch(request)
            .then((response) => {
              if (response.status === 200) {
                const responseClone = response.clone();
                caches.open(DYNAMIC_CACHE_NAME)
                  .then((cache) => {
                    cache.put(request, responseClone);
                  })
                  .catch((error) => {
                    console.warn('Failed to cache response:', error);
                  });
              }
            })
            .catch(() => {
              // Network failed, but we have cache
            });
          return cachedResponse;
        }

        // Not in cache, fetch from network
        return fetch(request)
          .then((response) => {
            // Cache successful responses (only for same-origin or allowed domains)
            if (response.status === 200 && (url.origin === self.location.origin || url.hostname.includes('fonts.googleapis.com') || url.hostname.includes('fonts.gstatic.com'))) {
              const responseClone = response.clone();
              caches.open(DYNAMIC_CACHE_NAME)
                .then((cache) => {
                  cache.put(request, responseClone);
                })
                .catch((error) => {
                  console.warn('Failed to cache response:', error);
                });
            }
            return response;
          })
          .catch((error) => {
            // Network failed and not in cache
            console.warn('Network request failed:', request.url, error);

            if (request.destination === 'image') {
              // Return placeholder for images
              return new Response(
                '<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="200" fill="#1e293b"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#ff014f">Image Offline</text></svg>',
                { headers: { 'Content-Type': 'image/svg+xml' } }
              );
            }

            // For external resources like fonts, fail silently instead of throwing
            if (url.origin !== self.location.origin) {
              return new Response('', { status: 404, statusText: 'Not Found' });
            }

            throw error;
          });
      })
  );
});

// Background sync for when connection is restored
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Handle any pending operations when back online
      Promise.resolve()
    );
  }
});

// Push notifications (for future use)
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/pwa-192x192.svg',
      badge: '/favicon-32x32.svg',
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: data.primaryKey || 1
      },
      actions: [
        {
          action: 'explore',
          title: 'View Portfolio',
          icon: '/favicon-32x32.svg'
        },
        {
          action: 'close',
          title: 'Close',
          icon: '/favicon-16x16.svg'
        }
      ]
    };

    event.waitUntil(
      self.registration.showNotification(data.title || 'Kenenis Dev Portfolio', options)
    );
  }
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});
