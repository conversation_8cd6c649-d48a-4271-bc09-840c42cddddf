import React, { useState, useEffect } from "react";
import { supabase } from "../services/supabase/client";
import { useQuery, useQueryClient } from "@tanstack/react-query";

export interface NotificationItem {
  id: string;
  type: "message" | "testimonial";
  title: string;
  content: string;
  sender: string;
  timestamp: string;
  isRead: boolean;
  data?: any; // Original message/testimonial data
}

export const useNotifications = () => {
  const queryClient = useQueryClient();

  // Fetch unread messages
  const { data: messages = [] } = useQuery({
    queryKey: ["unread-messages"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("contact_messages")
        .select("*")
        .eq("is_read", false)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data || [];
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Fetch unread testimonials
  const { data: testimonials = [] } = useQuery({
    queryKey: ["unread-testimonials"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("testimonials")
        .select("*")
        .eq("is_read", false)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data || [];
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Convert messages and testimonials to notifications
  const allNotifications = React.useMemo(() => {
    const messageNotifications: NotificationItem[] = messages.map((msg) => ({
      id: `message-${msg.id}`,
      type: "message" as const,
      title: `New message from ${msg.name}`,
      content: msg.subject || msg.message?.substring(0, 100) + "..." || "No subject",
      sender: msg.name,
      timestamp: msg.created_at,
      isRead: msg.is_read || false,
      data: msg,
    }));

    const testimonialNotifications: NotificationItem[] = testimonials.map((testimonial) => ({
      id: `testimonial-${testimonial.id}`,
      type: "testimonial" as const,
      title: `New testimonial from ${testimonial.name}`,
      content: testimonial.message?.substring(0, 100) + "..." || "No content",
      sender: testimonial.name,
      timestamp: testimonial.created_at,
      isRead: testimonial.is_read || false,
      data: testimonial,
    }));

    return [...messageNotifications, ...testimonialNotifications]
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }, [messages, testimonials]);

  // Mark notification as read
  const markAsRead = async (notificationId: string) => {
    const notification = allNotifications.find(n => n.id === notificationId);
    if (!notification) return;

    try {
      if (notification.type === "message") {
        const messageId = notification.id.replace("message-", "");
        await supabase
          .from("contact_messages")
          .update({ is_read: true })
          .eq("id", messageId);
        
        queryClient.invalidateQueries({ queryKey: ["unread-messages"] });
      } else if (notification.type === "testimonial") {
        const testimonialId = notification.id.replace("testimonial-", "");
        await supabase
          .from("testimonials")
          .update({ is_read: true })
          .eq("id", testimonialId);
        
        queryClient.invalidateQueries({ queryKey: ["unread-testimonials"] });
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      // Mark all unread messages as read
      if (messages.length > 0) {
        await supabase
          .from("contact_messages")
          .update({ is_read: true })
          .eq("is_read", false);
      }

      // Mark all unread testimonials as read
      if (testimonials.length > 0) {
        await supabase
          .from("testimonials")
          .update({ is_read: true })
          .eq("is_read", false);
      }

      queryClient.invalidateQueries({ queryKey: ["unread-messages"] });
      queryClient.invalidateQueries({ queryKey: ["unread-testimonials"] });
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
  };

  // Set up real-time subscriptions
  useEffect(() => {
    const messagesChannel = supabase
      .channel("notification_messages")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "contact_messages",
        },
        () => {
          queryClient.invalidateQueries({ queryKey: ["unread-messages"] });
        }
      )
      .subscribe();

    const testimonialsChannel = supabase
      .channel("notification_testimonials")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "testimonials",
        },
        () => {
          queryClient.invalidateQueries({ queryKey: ["unread-testimonials"] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(messagesChannel);
      supabase.removeChannel(testimonialsChannel);
    };
  }, [queryClient]);

  const unreadCount = allNotifications.filter(n => !n.isRead).length;

  return {
    notifications: allNotifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
  };
};
