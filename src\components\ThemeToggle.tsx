import React from 'react';
import { <PERSON>, Moon } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggle: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <div className="theme-toggle-container">
      <button
        onClick={toggleTheme}
        className="theme-toggle-btn group relative w-12 h-6 rounded-full transition-all duration-500 ease-in-out focus:outline-none focus:ring-2 focus:ring-red/50 focus:ring-offset-2 focus:ring-offset-transparent"
        aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`}
        role="switch"
        aria-checked={theme === 'light'}
      >
        {/* Toggle Background */}
        <div className={`absolute inset-0 rounded-full transition-all duration-500 ease-in-out ${
          theme === 'dark' 
            ? 'bg-gradient-to-r from-slate-700 via-slate-600 to-slate-700 shadow-inner' 
            : 'bg-gradient-to-r from-orange-300 via-yellow-300 to-orange-300 shadow-lg shadow-orange-200/50'
        }`}>
          {/* Background Glow */}
          <div className={`absolute inset-0 rounded-full transition-opacity duration-500 ${
            theme === 'dark'
              ? 'bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-blue-500/20 opacity-0 group-hover:opacity-100'
              : 'bg-gradient-to-r from-yellow-400/30 via-orange-400/30 to-yellow-400/30 opacity-100'
          }`}></div>
        </div>

        {/* Toggle Slider */}
        <div className={`absolute top-0.5 transition-all duration-500 ease-in-out transform ${
          theme === 'dark' ? 'translate-x-0.5' : 'translate-x-6.5'
        } w-5 h-5 rounded-full shadow-lg`}>
          {/* Slider Background */}
          <div className={`absolute inset-0 rounded-full transition-all duration-500 ${
            theme === 'dark'
              ? 'bg-gradient-to-br from-slate-200 to-slate-100 shadow-md'
              : 'bg-gradient-to-br from-yellow-100 to-white shadow-lg shadow-orange-200/50'
          }`}></div>
          
          {/* Icon Container */}
          <div className="absolute inset-0 flex items-center justify-center">
            {theme === 'dark' ? (
              <Moon
                size={10}
                className="text-slate-600 transition-all duration-300 group-hover:text-slate-700"
              />
            ) : (
              <Sun
                size={10}
                className="text-orange-500 transition-all duration-300 group-hover:text-orange-600 group-hover:rotate-90"
              />
            )}
          </div>
          
          {/* Slider Glow */}
          <div className={`absolute inset-0 rounded-full transition-all duration-500 ${
            theme === 'dark'
              ? 'shadow-lg shadow-blue-500/20 opacity-0 group-hover:opacity-100'
              : 'shadow-lg shadow-yellow-400/40'
          }`}></div>
        </div>

        {/* Stars for Dark Mode */}
        {theme === 'dark' && (
          <div className="absolute inset-0 overflow-hidden rounded-full pointer-events-none">
            <div className="absolute top-1.5 right-2 w-0.5 h-0.5 bg-white rounded-full animate-pulse"></div>
            <div className="absolute top-2 right-3.5 w-0.5 h-0.5 bg-white/70 rounded-full animate-pulse delay-300"></div>
            <div className="absolute bottom-1.5 right-2.5 w-0.5 h-0.5 bg-white/50 rounded-full animate-pulse delay-700"></div>
          </div>
        )}

        {/* Sun Rays for Light Mode */}
        {theme === 'light' && (
          <div className="absolute inset-0 overflow-hidden rounded-full pointer-events-none">
            <div className="absolute top-0.5 left-2 w-0.5 h-0.5 bg-yellow-400/60 rounded-full animate-pulse"></div>
            <div className="absolute bottom-0.5 left-2 w-0.5 h-0.5 bg-yellow-400/60 rounded-full animate-pulse delay-200"></div>
            <div className="absolute top-1.5 left-0.5 w-0.5 h-0.5 bg-yellow-400/60 rounded-full animate-pulse delay-400"></div>
            <div className="absolute top-1.5 left-3.5 w-0.5 h-0.5 bg-yellow-400/60 rounded-full animate-pulse delay-600"></div>
          </div>
        )}
      </button>
    </div>
  );
};

export default ThemeToggle;
