import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title = "Keneni<PERSON> Kero (Kenenis) - Full Stack Developer & UI/UX Designer | Portfolio",
  description = "Expert Full Stack Developer specializing in React, Node.js, TypeScript & UI/UX Design. 2+ years experience, 20+ projects completed. Available for freelance work.",
  keywords = "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Full Stack Developer, React Developer, Node.js, TypeScript, UI/UX Designer, Web Developer, Software Engineer, Frontend Developer, Backend Developer, JavaScript, HTML, CSS, Portfolio, Freelancer, Ethiopia Developer",
  image = "https://kenenis.com/og-image.svg",
  url = "https://kenenis.com/",
  type = "website",
  author = "<PERSON><PERSON><PERSON>",
  publishedTime,
  modifiedTime,
  section,
  tags = []
}) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "<PERSON>enisa Kero",
    "alternateName": "Kenenis",
    "url": url,
    "image": image,
    "sameAs": [
      "https://github.com/kenenis",
      "https://linkedin.com/in/kenenis",
      "https://twitter.com/kenenis"
    ],
    "jobTitle": "Full Stack Developer",
    "worksFor": {
      "@type": "Organization",
      "name": "Freelance"
    },
    "knowsAbout": [
      "React.js", "Node.js", "TypeScript", "JavaScript", "HTML", "CSS",
      "UI/UX Design", "Full Stack Development", "Web Development", "Software Engineering"
    ],
    "description": description,
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "Ethiopia"
    }
  };

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content={author} />
      <link rel="canonical" href={url} />

      {/* Open Graph */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={url} />
      <meta property="og:site_name" content="Kenenisa Kero Portfolio" />
      <meta property="og:locale" content="en_US" />
      
      {publishedTime && <meta property="article:published_time" content={publishedTime} />}
      {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
      {section && <meta property="article:section" content={section} />}
      {tags.map(tag => (
        <meta key={tag} property="article:tag" content={tag} />
      ))}

      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:creator" content="@kenenis" />
      <meta name="twitter:site" content="@kenenis" />

      {/* Additional SEO */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="bingbot" content="index, follow" />

      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
    </Helmet>
  );
};

export default SEOHead;
