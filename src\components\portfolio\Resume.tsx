import React, { useState, useEffect } from "react";
import { usePortfolioContent } from "../../hooks/usePortfolioContent";
import { supabase } from "../../services/supabase/client";

const Resume = () => {
  // Database integration
  const { content } = usePortfolioContent();

  // Resume content state with defaults
  const [resumeContent, setResumeContent] = useState({
    title: "My Resume",
    description: "Experience and education have been the pillars of my professional career, helping me build expertise in web development and design.",
    highlightedWords: [], // Words to highlight in descriptions
  });

  // Function to render text with highlighted words
  const renderTextWithHighlights = (text, highlightedWords = []) => {
    if (!highlightedWords || highlightedWords.length === 0) {
      return text;
    }

    let highlightedText = text;
    highlightedWords.forEach((word) => {
      const regex = new RegExp(`\\b(${word})\\b`, 'gi');
      highlightedText = highlightedText.replace(
        regex,
        '<span class="highlight-keyword">$1</span>'
      );
    });

    return <span dangerouslySetInnerHTML={{ __html: highlightedText }} />;
  };

  // Fallback education data
  const fallbackEducation = [
    {
      id: 1,
      degree: "High School",
      institution: "International Murad Hudavendigar Anatolian Imam Hatip Highschool",
      period: "2023-2025- present",
      description:
        "As a student at an international school in Türkiye, I learn alongside peers from every continent[95+ countries] - turning every classroom into a mini United Nations.",
    },
    {
      id: 2,
      degree: "Middle school",
      institution: "Ifa Boru Special Boarding School",
      period: "2020-2022",
      description:
        "At Ifa Boru Special Boarding School, weekends weren't for Netflix - they were for 'survival mode' group projects and library races. That crucible forged my ability to deliver exceptional work under tight deadlines.",
    },
    {
      id: 3,
      degree: "Elementary school",
      institution: "Amanuel Light & Life Primary School",
      period: "2014-2020",
      description:
        "Primary school graduate of Amanuel Light & Life Primary School, a rigorous private institution known for academic excellence, where I developed disciplined study habits and leadership skills in a competitive environment.",
    },
  ];

  // Fallback experience data
  const fallbackExperience = [
    {
      id: 1,
      position: "Assistant Workshop Director",
      company: "PROGEB Technologic team",
      period: "2024-present",
      description:
        "My co-leadership role required balancing three priorities: Technical Depth – Ensuring projects were challenging yet achievable Accessibility – Adapting explanations for visual/kinesthetic learners Operations – Coordinating volunteer schedules and equipment logistics.",
    },
    {
      id: 2,
      position: "Tech team leader",
      company: "Five Star Technology Club",
      period: "2022-2023",
      description:
        "I led the technology team for more than a year, where I got a comprehensive leadership skill by leading a group of like minded people.",
    },
    {
      id: 3,
      position: "Technology Helper",
      company: "Five Star Tech Club",
      period: "2021-2022",
      description:
        "Elected 'Technology Helper' for 1+ years - assisted teachers with AV equipment and basic troubleshooting",
    },
  ];

  // State for dynamic data
  const [education, setEducation] = useState(fallbackEducation);
  const [experience, setExperience] = useState(fallbackExperience);

  // Load content from database
  useEffect(() => {
    if (content && content.length > 0) {
      const resumeData = content.find(item => item.section === "resume")?.content;
      if (resumeData) {
        if (resumeData.title || resumeData.description) {
          setResumeContent(prev => ({
            title: resumeData.title || prev.title,
            description: resumeData.description || prev.description,
          }));
        }

        if (resumeData.education && resumeData.education.length > 0) {
          setEducation(resumeData.education);
        }

        if (resumeData.experience && resumeData.experience.length > 0) {
          setExperience(resumeData.experience);
        }
      }
    }
  }, [content]);

  // Real-time subscription for resume content updates
  useEffect(() => {
    const channel = supabase
      .channel("resume_content_changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "portfolio_content",
          filter: "section=eq.resume",
        },
        (payload) => {
          if (
            payload.eventType === "UPDATE" ||
            payload.eventType === "INSERT"
          ) {
            const newContent = payload.new as any;
            if (newContent.content) {

              if (newContent.content.title || newContent.content.description) {
                setResumeContent(prev => ({
                  title: newContent.content.title || prev.title,
                  description: newContent.content.description || prev.description,
                }));
              }

              if (newContent.content.education) {
                setEducation(newContent.content.education);
              }

              if (newContent.content.experience) {
                setExperience(newContent.content.experience);
              }
            }
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  return (
    <section
      id="resume"
      className="section bg-dark-darker relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="resume-bg-glow"></div>
      <div className="resume-particles"></div>
      <div className="resume-grid-pattern"></div>

      <div className="container mx-auto relative z-10">
        {/* Enhanced Title Section */}
        <div className="text-center mb-16">
          <div className="relative inline-block">
            <h2 className="enhanced-resume-title">{resumeContent.title}</h2>
            <div className="resume-title-glow"></div>
            <div className="resume-title-underline"></div>
          </div>

          <div className="resume-description-container">
            <p className="resume-description">
              {renderTextWithHighlights(resumeContent.description, resumeContent.highlightedWords)}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
          {/* Enhanced Education */}
          <div className="education-section">
            <div className="relative mb-10">
              <h3 className="enhanced-section-subtitle">Education</h3>
              <div className="subtitle-glow"></div>
              <div className="subtitle-line"></div>
            </div>

            <div className="timeline-container">
              {education.map((item, index) => (
                <div
                  key={index}
                  className="enhanced-timeline-item education-item"
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <div className="timeline-connector"></div>
                  <div className="timeline-dot"></div>

                  <div className="timeline-content">
                    <div className="period-badge">{item.period}</div>
                    <div className="timeline-header">
                      <h4 className="timeline-title">{item.degree}</h4>
                      <p className="timeline-institution">{item.institution}</p>
                    </div>
                    <p className="timeline-description">{item.description}</p>
                  </div>

                  <div className="timeline-glow"></div>
                  <div className="timeline-particles"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Enhanced Experience */}
          <div className="experience-section">
            <div className="relative mb-10">
              <h3 className="enhanced-section-subtitle">Experience</h3>
              <div className="subtitle-glow"></div>
              <div className="subtitle-line"></div>
            </div>

            <div className="timeline-container">
              {experience.map((item, index) => (
                <div
                  key={index}
                  className="enhanced-timeline-item experience-item"
                  style={{ animationDelay: `${index * 0.2 + 0.6}s` }}
                >
                  <div className="timeline-connector"></div>
                  <div className="timeline-dot"></div>

                  <div className="timeline-content">
                    <div className="period-badge">{item.period}</div>
                    <div className="timeline-header">
                      <h4 className="timeline-title">{item.position}</h4>
                      <p className="timeline-institution">{item.company}</p>
                    </div>
                    <p className="timeline-description">{item.description}</p>
                  </div>

                  <div className="timeline-glow"></div>
                  <div className="timeline-particles"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Resume;
