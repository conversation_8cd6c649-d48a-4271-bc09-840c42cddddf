import { useState } from "react";
import { supabase } from "../services/supabase/client";
import { toast } from "sonner";

export interface UploadOptions {
  bucket?: string;
  folder?: string;
  maxSize?: number; // in MB
  allowedTypes?: string[];
}

export const useFileUpload = () => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const uploadFile = async (
    file: File,
    options: UploadOptions = {}
  ): Promise<{ success: boolean; url?: string; error?: string }> => {
    const {
      bucket = "portfolio-assets",
      folder = "uploads",
      maxSize = 5, // 5MB default
      allowedTypes = ["image/jpeg", "image/png", "image/webp", "image/gif"],
    } = options;

    try {
      setUploading(true);
      setProgress(0);

      // Validate file size
      if (file.size > maxSize * 1024 * 1024) {
        toast.error(`File size must be less than ${maxSize}MB`);
        return { success: false, error: `File too large (max ${maxSize}MB)` };
      }

      // Validate file type
      if (!allowedTypes.includes(file.type)) {
        toast.error("Invalid file type. Please upload an image.");
        return { success: false, error: "Invalid file type" };
      }

      // Generate unique filename
      const fileExt = file.name.split(".").pop();
      const fileName = `${Date.now()}-${Math.random()
        .toString(36)
        .substring(2)}.${fileExt}`;
      const filePath = `${folder}/${fileName}`;

      // Upload file to Supabase Storage
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(filePath, file, {
          cacheControl: "3600",
          upsert: false,
        });

      if (error) {
        toast.error("Failed to upload file");
        return { success: false, error: error.message };
      }

      // Get public URL
      const {
        data: { publicUrl },
      } = supabase.storage.from(bucket).getPublicUrl(filePath);

      setProgress(100);
      toast.success("File uploaded successfully!");

      return { success: true, url: publicUrl };
    } catch (error) {
      toast.error("An unexpected error occurred during upload");
      return { success: false, error: "Upload failed" };
    } finally {
      setUploading(false);
      setTimeout(() => setProgress(0), 1000);
    }
  };

  const uploadMultipleFiles = async (
    files: FileList | File[],
    options: UploadOptions = {}
  ): Promise<{ success: boolean; urls?: string[]; errors?: string[] }> => {
    const fileArray = Array.from(files);
    const results = await Promise.all(
      fileArray.map((file) => uploadFile(file, options))
    );

    const successful = results.filter((r) => r.success);
    const failed = results.filter((r) => !r.success);

    if (successful.length > 0) {
      toast.success(`${successful.length} file(s) uploaded successfully!`);
    }

    if (failed.length > 0) {
      toast.error(`${failed.length} file(s) failed to upload`);
    }

    return {
      success: successful.length > 0,
      urls: successful.map((r) => r.url!),
      errors: failed.map((r) => r.error!),
    };
  };

  const deleteFile = async (
    url: string,
    bucket: string = "portfolio-assets"
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      // Extract file path from URL
      const urlParts = url.split("/");
      const bucketIndex = urlParts.findIndex((part) => part === bucket);

      if (bucketIndex === -1) {
        return { success: false, error: "Invalid file URL" };
      }

      const filePath = urlParts.slice(bucketIndex + 1).join("/");

      const { error } = await supabase.storage.from(bucket).remove([filePath]);

      if (error) {
        toast.error("Failed to delete file");
        return { success: false, error: error.message };
      }

      toast.success("File deleted successfully!");
      return { success: true };
    } catch (error) {
      toast.error("An unexpected error occurred during deletion");
      return { success: false, error: "Delete failed" };
    }
  };

  const getFileUrl = (bucket: string, filePath: string): string => {
    const {
      data: { publicUrl },
    } = supabase.storage.from(bucket).getPublicUrl(filePath);

    return publicUrl;
  };

  return {
    uploadFile,
    uploadMultipleFiles,
    deleteFile,
    getFileUrl,
    uploading,
    progress,
  };
};
