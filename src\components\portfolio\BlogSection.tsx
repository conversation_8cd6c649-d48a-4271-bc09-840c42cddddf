import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Clock, ArrowRight, Eye, MessageCircle } from 'lucide-react';
import { useBlogPosts } from '../../hooks/useBlog';
import { motion } from 'framer-motion';

const BlogSection = () => {
  const { publishedPosts, isLoadingPublished } = useBlogPosts();

  // Get the latest 3 blog posts
  const latestPosts = publishedPosts?.slice(0, 3) || [];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      'DEVELOPMENT': 'bg-gradient-to-r from-blue-500 to-cyan-500',
      'DESIGN': 'bg-gradient-to-r from-purple-500 to-pink-500',
      'APPLICATION': 'bg-gradient-to-r from-green-500 to-emerald-500',
      'TECHNOLOGY': 'bg-gradient-to-r from-orange-500 to-red-500',
      'TUTORIAL': 'bg-gradient-to-r from-indigo-500 to-purple-500',
      'CANADA': 'bg-gradient-to-r from-red-500 to-red-600',
    };
    return colors[category as keyof typeof colors] || 'bg-gradient-to-r from-gray-500 to-gray-600';
  };

  if (isLoadingPublished) {
    return (
      <section id="blog" className="section bg-gray-50 dark:bg-dark-darker relative overflow-hidden">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded-lg w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white dark:bg-dark-lighter rounded-2xl shadow-lg animate-pulse border border-gray-200 dark:border-gray-700">
                <div className="h-48 bg-gray-300 dark:bg-gray-700 rounded-t-2xl"></div>
                <div className="p-6">
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-20 mb-3"></div>
                  <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded mb-3"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full mb-2"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="blog" className="section bg-gray-50 dark:bg-dark-darker relative overflow-hidden">
      {/* Background Effects - matching Portfolio section */}
      <div className="portfolio-bg-glow"></div>
      <div className="portfolio-particles"></div>
      <div className="portfolio-grid-pattern"></div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Enhanced Title Section - matching Portfolio section */}
        <div className="text-center mb-16">
          <div className="relative inline-block">
            <h2 className="enhanced-portfolio-title text-gray-900 dark:text-white">My Blog</h2>
            <div className="portfolio-title-glow"></div>
            <div className="portfolio-title-underline"></div>
          </div>

          <div className="portfolio-description-container">
            <p className="portfolio-description text-gray-600 dark:text-gray-300">
              Discover insights, tutorials, and thoughts on development, design, and technology
            </p>
          </div>
        </div>

        {/* Blog Posts Grid - Enhanced styling matching Portfolio */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {latestPosts.map((post, index) => (
            <motion.article
              key={post.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="enhanced-portfolio-item group"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <Link to={`/blog/${post.slug}`} className="block">
                <div className="portfolio-card bg-white dark:bg-dark-lighter rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden group-hover:scale-[1.02] border border-gray-200 dark:border-gray-700 hover:border-red/50">
                  {/* Featured Image */}
                  <div className="relative h-48 overflow-hidden">
                    {post.featured_image ? (
                      <img
                        src={post.featured_image}
                        alt={post.title}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 flex items-center justify-center">
                        <div className="text-white text-6xl font-bold opacity-20">
                          {post.title.charAt(0)}
                        </div>
                      </div>
                    )}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    {/* Category */}
                    {post.categories && post.categories.length > 0 && (
                      <div className="mb-3">
                        <span className={`inline-block px-3 py-1 text-xs font-semibold text-white rounded-full ${getCategoryColor(post.categories[0].name)}`}>
                          {post.categories[0].name}
                        </span>
                      </div>
                    )}

                    {/* Title */}
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-red transition-colors duration-300 line-clamp-2">
                      {post.title}
                    </h3>

                    {/* Excerpt */}
                    <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3 text-sm leading-relaxed">
                      {post.excerpt || post.content?.substring(0, 120) + '...'}
                    </p>

                    {/* Meta Information */}
                    <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>{formatDate(post.published_at || post.created_at)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="w-4 h-4" />
                          <span>{post.reading_time || 5} min read</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-1">
                          <Eye className="w-4 h-4" />
                          <span>{post.views_count || 0}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            </motion.article>
          ))}
        </div>

        {/* View All Button - Enhanced styling matching Portfolio */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center"
        >
          <Link
            to="/blog"
            className="inline-flex items-center space-x-2 bg-gradient-to-r from-red to-red-light hover:from-red-light hover:to-red text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 border border-red/30 hover:border-red/50"
          >
            <span>View All Posts</span>
            <ArrowRight className="w-5 h-5" />
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default BlogSection;
