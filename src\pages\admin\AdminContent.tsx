import React, { useState, useEffect } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import {
  Save,
  Edit3,
  Image,
  Type,
  Palette,
  FileText,
  User,
  Briefcase,
  Star,
  MessageSquare,
  Plus,
  Trash2,
  Eye,
  Upload,
  Check,
  X,
  Phone,
  Mail,
  MapPin,
  Clock,
  Linkedin,
  Github,
  Twitter,
  Instagram,
  Facebook,
  RefreshCw,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { usePortfolioContent, usePortfolioItems } from "@/hooks/usePortfolioContent";
import { usePortfolioCategories } from "@/hooks/usePortfolioCategories";
import { useFileUpload } from "@/hooks/useFileUpload";

const AdminContent = () => {
  const [activeTab, setActiveTab] = useState("hero");
  const [isEditing, setIsEditing] = useState(false);
  const [saveStatus, setSaveStatus] = useState("idle"); // "idle", "saving", "saved", "error"

  // Category management state
  const [editingCategory, setEditingCategory] = useState(null);
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [categoryFormData, setCategoryFormData] = useState({
    name: "",
    description: "",
    color: "#ef4444",
    display_order: 0,
    is_active: true,
  });

  // Portfolio management state
  const [editingPortfolioItem, setEditingPortfolioItem] = useState(null);
  const [showPortfolioForm, setShowPortfolioForm] = useState(false);
  const [portfolioFormData, setPortfolioFormData] = useState({
    title: "",
    description: "",
    category: "",
    image_url: "",
    technologies: [],
    live_url: "",
    github_url: "",
    featured: false,
  });

  // Feature management state
  const [editingFeature, setEditingFeature] = useState(null);
  const [showFeatureForm, setShowFeatureForm] = useState(false);
  const [featureFormData, setFeatureFormData] = useState({
    title: "",
    description: "",
    icon: "",
  });

  // Resume management state
  const [editingResumeItem, setEditingResumeItem] = useState(null);
  const [showResumeForm, setShowResumeForm] = useState(false);
  const [resumeFormType, setResumeFormType] = useState("education"); // "education" or "experience"
  const [resumeFormData, setResumeFormData] = useState({
    degree: "",
    institution: "",
    position: "",
    company: "",
    period: "",
    description: "",
  });

  // Database hooks
  const { content, getContentBySection, updateContent, isUpdating } =
    usePortfolioContent();
  const {
    portfolioItems: dbPortfolioItems,
    createItem,
    updateItem,
    deleteItem,
    isCreating,
    isUpdating: isUpdatingItem,
    isDeleting
  } = usePortfolioItems();

  const {
    categories: dbCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    getCategoryNames,
    isCreating: isCreatingCategory,
    isUpdating: isUpdatingCategory,
    isDeleting: isDeletingCategory,
  } = usePortfolioCategories();

  const { uploadFile, uploading } = useFileUpload();

  const contentSections = [
    { id: "hero", label: "Hero Section", icon: Type },
    { id: "about", label: "About Me", icon: User },
    { id: "resume", label: "Resume", icon: FileText },
    { id: "skills", label: "Skills", icon: Star },
    { id: "portfolio", label: "Portfolio", icon: Briefcase },
    { id: "testimonials", label: "Testimonials", icon: MessageSquare },
    { id: "contact", label: "Contact Info", icon: MessageSquare },
  ];

  const [heroContent, setHeroContent] = useState({
    name: "Kenenisa Kero",
    profession: "Full Stack Developer",
    welcomeText: "WELCOME TO MY WORLD",
    description:
      "I'm a creative full stack developer specializing in building exceptional digital experiences. I build blazing-fast, scalable web apps—from pixel-perfect UIs to robust backends. Currently crafting AI-powered solutions and open-source tools to simplify workflows.",
    buttonText: "More About Me",
    profileImage:
      "https://i.postimg.cc/FH1L6nNj/1724878552606-2.jpg",
    typingTexts: ["Full Stack Developer", "UI/UX Designer", "Web Developer"],
    professions: [
      "Full Stack Developer",
      "UI/UX Designer",
      "Web Developer",
      "Software Engineer",
    ],
  });

  const [aboutContent, setAboutContent] = useState({
    title: "About Me",
    description:
      "I'm a passionate web developer with 5+ years of experience creating digital solutions.",
    experience: "5+ Years",
    projects: "100+ Projects",
    clients: "50+ Clients",
    highlightedWords: [],
    features: [
      {
        id: 1,
        title: "Problem-Solving Approach",
        description: "I don’t just write code—I solve business challenges with technology.",
        icon: "🎯",
      },
      {
        id: 2,
        title: "Website Development",
        description: "I build fast, secure, and scalable modern web apps with modern design principles",
        icon: "💻",
      },
      {
        id: 3,
        title: "Marketing & Reporting",
        description: "I optimize apps for real-world impact and Data-Driven Decisions.",
        icon: "📊",
      },
      {
        id: 4,
        title: "Client Collaboration",
        description: "I bridge tech and business needs with a comprehensive negotiation.",
        icon: "🤝",
      },
      {
        id: 5,
        title: "From Idea to MVP in Days",
        description: "I accelerate development without sacrificing quality",
        icon: "🚀",
      },
    ],
  });

  const [skillsContent, setSkillsContent] = useState({
    title: "Professional Skills",
    description: "My technical expertise and proficiency levels across various technologies and tools that drive innovation in web development.",
    highlightedWords: [],
  });

  const [skills, setSkills] = useState([
     { name: "HTML, CSS & Bootstrap", percentage: 95 },
      { name: "JavaScript", percentage: 89 },
      { name: " TypeScript", percentage: 88 },
      { name: "React.js", percentage: 90 },
      { name: "Node.js & Express.js", percentage: 88 },
      { name: "MySQL & Rest API", percentage: 90 },
      { name: "Wordpress, Adobe photoshop & Figma", percentage: 78 },
      { name: "BashScript, Git & GitHub", percentage: 88 },
  ]);

  // Resume state
  const [resumeContent, setResumeContent] = useState({
    title: "My Resume",
    description: "Experience and education have been the pillars of my professional career, helping me build expertise in web development and design.",
    highlightedWords: [],
  });

  const [education, setEducation] = useState([
    {
      id: 1,
      degree: "High School",
      institution: "International Murad Hudavendigar Anatolian Imam Hatip Highschool",
      period: "2023-2025- present",
      description:
        "As a student at an international school in Türkiye, I learn alongside peers from every continent[95+ countries] - turning every classroom into a mini United Nations.",
    },
    {
      id: 2,
      degree: "Middle school",
      institution: "Ifa Boru Special Boarding School",
      period: "2020-2022",
      description:
        "At Ifa Boru Special Boarding School, weekends weren't for Netflix - they were for 'survival mode' group projects and library races. That crucible forged my ability to deliver exceptional work under tight deadlines.",
    },
    {
      id: 3,
      degree: "Elementary school",
      institution: "Amanuel Light & Life Primary School",
      period: "2014-2020",
      description:
        "Primary school graduate of Amanuel Light & Life Primary School, a rigorous private institution known for academic excellence, where I developed disciplined study habits and leadership skills in a competitive environment.",
    },
  ]);

  const [experience, setExperience] = useState([
    {
      id: 1,
      position: "Assistant Workshop Director",
      company: "PROGEB Technologic team",
      period: "2024-present",
      description:
        "My co-leadership role required balancing three priorities: Technical Depth – Ensuring projects were challenging yet achievable Accessibility – Adapting explanations for visual/kinesthetic learners Operations – Coordinating volunteer schedules and equipment logistics.",
    },
    {
      id: 2,
      position: "Tech team leader",
      company: "Five Star Technology Club",
      period: "2022-2023",
      description:
        "I led the technology team for more than a year, where I got a comprehensive leadership skill by leading a group of like minded people.",
    },
    {
      id: 3,
      position: "Technology Helper",
      company: "Five Star Tech Club",
      period: "2021-2022",
      description:
        "Elected 'Technology Helper' for 1+ years - assisted teachers with AV equipment and basic troubleshooting",
    },
  ]);

  // Portfolio content state
  const [portfolioContent, setPortfolioContent] = useState({
    title: "My Portfolio",
    description: "Discover my latest creative works and innovative solutions across various digital platforms and technologies.",
    highlightedWords: [],
  });

  const [portfolioItems, setPortfolioItems] = useState([
    {
      id: 1,
      title: "Campus & School Attendance Tracker App",
      description: "AI powered secure attendance tracking app",
      category: "Real World App",
      image: "https://i.ibb.co/S4gX2cZB/20250712-021640-0000.png",
      technologies: ["React js", "TypeScript", "Node.js", "PostgreSQL"],
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/example",
      featured: true,
    },
    {
      id: 2,
      title: "Amazon clone",
      description: "Modern Amazon clone with React, Node.js, & Firebase auth",
      category: "Web dev",
      image: "https://www.webdevelopmentindia.biz/wp-content/uploads/2024/09/amazon-clone-development-amazon-clone-budget.webp",
      technologies: ["React.js", "Node.js", "Firebase", "MySQL"],
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/example",
      featured: false,
    },
    {
      id: 3,
      title: "Netflix clone",
      description: "Netflix clone with react.js and Firebase auth.",
      category: "Web dev",
      image: "https://img-s3.onedio.com/id-684133b82b739bb8d69abda2/rev-0/w-600/h-337/f-jpg/s-093c8f79946ff192d7575b1cfc61bac079ce8fbe.jpg",
      technologies: ["HTML", "CSS", "React", "Firebase"],
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/example",
      featured: false,
    },
    {
      id: 4,
      title: "Apple.com clone",
      description: "Latest Apple.com clone with react.js and Bootstrap.",
      category: "Web dev",
      image: "https://miro.medium.com/v2/resize:fit:1400/1*upQ06nwYQVsTVY4sX2B_rA.png",
      technologies: ["HTML", "CSS", "JavaScript", "Bootstrap"],
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/example",
      featured: false,
    },
    {
      id: 5,
      title: "Portfolio website",
      description: "Portfolio website using HTML, CSS, and JavaScript with modern design principles.",
      category: "Web Design",
      image: "https://mir-s3-cdn-cf.behance.net/projects/404/1a19e1184177031.Y3JvcCw0NjAyLDM2MDAsMTAyLDA.jpg",
      technologies: ["Html", "CSS", "javaScript", "Framer Motion"],
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/example",
      featured: false,
    },
    {
      id: 6,
      title: "Full stack portfolio",
      description: "Full stack portfolio website with SQL database created with modern design principles.",
      category: "UI/UX Design",
      image:  "https://i.ibb.co/fYw9mwFF/Screenshot-2025-07-03-234245.png",
      technologies: ["HTML", "CSS", "JavaScript", "React", "TypeScript", "MySQL"],
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/example",
      featured: false,
    },
  ]);

  const [testimonials, setTestimonials] = useState([
    {
      id: 1,
      name: "Mehmet Şah Çelik",
      company: "PROGEB UMHAIHL.",
      position: "Coordinator",
      message:
        "Kenenis was a real pleasure to work with and we look forward to working with him again. He's definitely the kind of developer you can trust with a project from start to finish.",
      rating: 5,
      image: "https://xxawmsmnsgadqagkfuqp.supabase.co/storage/v1/object/public/testimonial-images/1751616042154-6t7jpd0gtmw.jpg",
      featured: true,
    },
    {
      id: 2,
      name: "Adugna Bekele",
      company: "Evangadi Tech",
      position: "CEO",
      message: "Professional, creative, and delivered on time.",
      rating: 5,
      image: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA4klEQVR4AWIYeAAgdI5xCIiiMAo/EIBCsAq1RmslkNgDQKImgl4CVqCSkFDDOiQEKg2HjLi5iim+N8XM+ec9Wx9OOGCg3zngshjtffjQRA4GMg6jghFSIvQgjYjhCGKDvloPoY07GvCL2xawQPw7sEZfxR3cUIZXxFlcsENMDdjGORxxx1YP9BCwiU8YY4C9HhiibRPPEEUNBzmwxBUXmzgBg7oeWOGBkorzVjz9xVADIcxRFbEbRZwxRVzEfwMBZKzn94MkJuiKP0uvRYMCHZaQsGFWqGuYUVMnHDND1QwCAAAvmFJ7kzqQ5QAAAABJRU5ErkJggg==",
      featured: true,
    },
    {
      id: 3,
      name: "Zeki Yaslaş",
      company: "UMHAIHL",
      position: "School Director",
      message: "Kenenis is a very hardworking and dedicated person. He is a very good developer and I highly recommend him.",
      rating: 5,
      image: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSWDJSM-OVaMPRSogPXUQSrlxteW3PQWPB8OA&s",
      featured: false,
    },
  ]);

  const [contactInfo, setContactInfo] = useState({
    phone: "+90 551 898 60 38",
    email: "<EMAIL>",
    address: "Osmangazi, Bursa, Turkey",
    socialLinks: {
      linkedin: "https://www.linkedin.com/in/kenenisa-kero/",
      github: "https://github.com/kenenDev",
      twitter: "https://www.linkedin.com/in/kenenisa-kero/",
      instagram: "https://www.linkedin.com/in/kenenisa-kero/",
      facebook: "https://www.linkedin.com/in/kenenisa-kero/",
    },
    socialVisibility: {
      linkedin: true,
      github: true,
      twitter: true,
      instagram: true,
      facebook: true,
    },
    availability: "Available for freelance work",
    timezone: "EST (UTC-5)",
  });

  // Load content from database when available
  useEffect(() => {
    if (content && content.length > 0) {
      const heroData = content.find((item) => item.section === "hero")?.content;
      if (heroData) {
        setHeroContent((prev) => ({ ...prev, ...heroData }));
      }

      const aboutData = content.find(
        (item) => item.section === "about"
      )?.content;
      if (aboutData) {
        setAboutContent((prev) => ({ ...prev, ...aboutData }));
      }

      const skillsData = content.find(
        (item) => item.section === "skills"
      )?.content;
      if (skillsData) {
        if (skillsData.title || skillsData.description || skillsData.highlightedWords) {
          setSkillsContent(prev => ({
            ...prev,
            title: skillsData.title || prev.title,
            description: skillsData.description || prev.description,
            highlightedWords: skillsData.highlightedWords || prev.highlightedWords,
          }));
        }
        if (skillsData.skills) {
          setSkills(skillsData.skills);
        }
      }

      const contactData = content.find(
        (item) => item.section === "contact"
      )?.content;
      if (contactData) {
        setContactInfo((prev) => ({ ...prev, ...contactData }));
      }

      const resumeData = content.find(
        (item) => item.section === "resume"
      )?.content;
      if (resumeData) {
        if (resumeData.title || resumeData.description) {
          setResumeContent((prev) => ({ ...prev, ...resumeData }));
        }
        if (resumeData.education) {
          setEducation(resumeData.education);
        }
        if (resumeData.experience) {
          setExperience(resumeData.experience);
        }
      }

      const portfolioData = content.find(
        (item) => item.section === "portfolio"
      )?.content;
      if (portfolioData) {
        if (portfolioData.title || portfolioData.description || portfolioData.highlightedWords) {
          setPortfolioContent(prev => ({
            ...prev,
            title: portfolioData.title || prev.title,
            description: portfolioData.description || prev.description,
            highlightedWords: portfolioData.highlightedWords || prev.highlightedWords,
          }));
        }
      }
    }
  }, [content]);

  const handleSave = async () => {
    try {
      setSaveStatus("saving");

      // Save content based on active tab
      switch (activeTab) {
        case "hero":
          await updateContent({ section: "hero", content: heroContent });
          break;
        case "about":
          await updateContent({ section: "about", content: aboutContent });
          break;
        case "skills":
          await updateContent({
            section: "skills",
            content: {
              ...skillsContent,
              skills
            }
          });
          break;
        case "resume":
          await updateContent({
            section: "resume",
            content: {
              ...resumeContent,
              education,
              experience
            }
          });
          break;
        case "contact":
          await updateContent({ section: "contact", content: contactInfo });
          break;
        case "portfolio":
          await updateContent({
            section: "portfolio",
            content: {
              ...portfolioContent,
              items: portfolioItems
            },
          });
          break;
        case "testimonials":
          await updateContent({
            section: "testimonials",
            content: { items: testimonials },
          });
          break;
        default:
          toast.info(`${activeTab} section will be implemented soon`);
          return;
      }

      setSaveStatus("saved");
      setIsEditing(false);

      // Reset save status after 3 seconds
      setTimeout(() => setSaveStatus("idle"), 3000);
    } catch (error) {
      console.error("Error saving content:", error);
      setSaveStatus("error");
      toast.error("Failed to save content");

      // Reset save status after 3 seconds
      setTimeout(() => setSaveStatus("idle"), 3000);
    }
  };

  const renderHeroEditor = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Name
          </label>
          <Input
            value={heroContent.name}
            onChange={(e) =>
              setHeroContent({ ...heroContent, name: e.target.value })
            }
            className="bg-dark-bg border-red/30 text-white"
            disabled={!isEditing}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Profession
          </label>
          <Input
            value={heroContent.profession}
            onChange={(e) =>
              setHeroContent({ ...heroContent, profession: e.target.value })
            }
            className="bg-dark-bg border-red/30 text-white"
            disabled={!isEditing}
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Welcome Text
        </label>
        <Input
          value={heroContent.welcomeText}
          onChange={(e) =>
            setHeroContent({ ...heroContent, welcomeText: e.target.value })
          }
          className="bg-dark-bg border-red/30 text-white"
          disabled={!isEditing}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Profile Image
        </label>
        <div className="space-y-3">
          <Input
            value={heroContent.profileImage}
            onChange={(e) =>
              setHeroContent({ ...heroContent, profileImage: e.target.value })
            }
            className="bg-dark-bg border-red/30 text-white text-sm break-all"
            disabled={!isEditing}
            placeholder="https://example.com/image.jpg"
          />
          {isEditing && (
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3">
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="border-red/30 text-red hover:bg-red/10 text-xs sm:text-sm w-full sm:w-auto"
                disabled={uploading}
                onClick={() => {
                  const input = document.createElement("input");
                  input.type = "file";
                  input.accept = "image/*";
                  input.onchange = async (e) => {
                    const file = (e.target as HTMLInputElement).files?.[0];
                    if (file) {
                      try {
                        const url = await uploadFile(file, "profile-images");
                        setHeroContent({ ...heroContent, profileImage: url });
                        toast.success("Profile image uploaded successfully!");
                      } catch (error) {
                        toast.error("Failed to upload image");
                      }
                    }
                  };
                  input.click();
                }}
              >
                {uploading ? (
                  <div className="flex items-center gap-1 sm:gap-2">
                    <div className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-red/30 border-t-red rounded-full animate-spin"></div>
                    <span className="hidden sm:inline">Uploading...</span>
                    <span className="sm:hidden">Upload...</span>
                  </div>
                ) : (
                  <>
                    <Upload className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                    <span className="hidden sm:inline">Upload Image</span>
                    <span className="sm:hidden">Upload</span>
                  </>
                )}
              </Button>
              {heroContent.profileImage && (
                <div className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm text-gray-400">
                  <Image className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="hidden sm:inline">Preview available</span>
                  <span className="sm:hidden">Preview</span>
                </div>
              )}
            </div>
          )}
          {heroContent.profileImage && (
            <div className="mt-3">
              <img
                src={heroContent.profileImage}
                alt="Profile preview"
                className="w-20 h-20 rounded-full object-cover border-2 border-red/30"
              />
            </div>
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Description
        </label>
        <Textarea
          value={heroContent.description}
          onChange={(e) =>
            setHeroContent({ ...heroContent, description: e.target.value })
          }
          className="bg-dark-bg border-red/30 text-white min-h-[100px]"
          disabled={!isEditing}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Typing Animation Texts (comma-separated)
        </label>
        <Input
          value={heroContent.typingTexts.join(", ")}
          onChange={(e) =>
            setHeroContent({
              ...heroContent,
              typingTexts: e.target.value
                .split(",")
                .map((text) => text.trim())
                .filter((text) => text),
            })
          }
          className="bg-dark-bg border-red/30 text-white"
          disabled={!isEditing}
          placeholder="Web Developer, UI/UX Designer, Freelancer"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Mobile Professions (comma-separated)
        </label>
        <Input
          value={heroContent.professions.join(", ")}
          onChange={(e) =>
            setHeroContent({
              ...heroContent,
              professions: e.target.value
                .split(",")
                .map((text) => text.trim())
                .filter((text) => text),
            })
          }
          className="bg-dark-bg border-red/30 text-white"
          disabled={!isEditing}
          placeholder="Web Developer, UI/UX Designer, Freelancer, Creative Designer"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Button Text
        </label>
        <Input
          value={heroContent.buttonText}
          onChange={(e) =>
            setHeroContent({ ...heroContent, buttonText: e.target.value })
          }
          className="bg-dark-bg border-red/30 text-white"
          disabled={!isEditing}
        />
      </div>
    </div>
  );

  const renderAboutEditor = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Section Title
        </label>
        <Input
          value={aboutContent.title}
          onChange={(e) =>
            setAboutContent({ ...aboutContent, title: e.target.value })
          }
          className="bg-dark-bg border-red/30 text-white"
          disabled={!isEditing}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Description
        </label>
        <Textarea
          value={aboutContent.description}
          onChange={(e) =>
            setAboutContent({ ...aboutContent, description: e.target.value })
          }
          className="bg-dark-bg border-red/30 text-white min-h-[120px]"
          disabled={!isEditing}
          placeholder="Enter your about description. Use double line breaks (press Enter twice) to create separate paragraphs."
        />
        <p className="text-xs text-gray-400 mt-1">
          Tip: Use double line breaks (Enter twice) to create separate paragraphs
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Highlighted Words (comma-separated)
        </label>
        <Input
          value={aboutContent.highlightedWords?.join(", ") || ""}
          onChange={(e) =>
            setAboutContent({
              ...aboutContent,
              highlightedWords: e.target.value
                .split(",")
                .map((word) => word.trim())
                .filter((word) => word),
            })
          }
          className="bg-dark-bg border-red/30 text-white"
          disabled={!isEditing}
          placeholder="e.g., passionate, developer, experience, solutions"
        />
        <p className="text-xs text-gray-400 mt-1">
          Words that will be highlighted in red in the description
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Experience
          </label>
          <Input
            value={aboutContent.experience}
            onChange={(e) =>
              setAboutContent({ ...aboutContent, experience: e.target.value })
            }
            className="bg-dark-bg border-red/30 text-white"
            disabled={!isEditing}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Projects
          </label>
          <Input
            value={aboutContent.projects}
            onChange={(e) =>
              setAboutContent({ ...aboutContent, projects: e.target.value })
            }
            className="bg-dark-bg border-red/30 text-white"
            disabled={!isEditing}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Clients
          </label>
          <Input
            value={aboutContent.clients}
            onChange={(e) =>
              setAboutContent({ ...aboutContent, clients: e.target.value })
            }
            className="bg-dark-bg border-red/30 text-white"
            disabled={!isEditing}
          />
        </div>
      </div>

      {/* Feature Boxes Management */}
      <div className="bg-dark-bg/50 rounded-lg border border-red/20 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4 mb-4">
          <h3 className="text-base sm:text-lg font-semibold text-white">Feature Boxes</h3>
          <Button
            onClick={() => {
              setEditingFeature(null);
              setFeatureFormData({
                title: "",
                description: "",
                icon: "",
              });
              setShowFeatureForm(true);
            }}
            size="sm"
            className="bg-red hover:bg-red/80 text-xs sm:text-sm w-full sm:w-auto"
          >
            <Plus className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            Add Feature
          </Button>
        </div>

        <div className="space-y-3 sm:space-y-4">
          {aboutContent.features.map((feature) => (
            <div
              key={feature.id}
              className="bg-dark-bg/30 rounded-lg border border-red/10 p-3 sm:p-4"
            >
              <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-3">
                <div className="flex items-start gap-2 sm:gap-3 flex-1">
                  <div className="text-xl sm:text-2xl flex-shrink-0">{feature.icon}</div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-white font-semibold text-sm sm:text-base truncate">{feature.title}</h4>
                    <p className="text-gray-300 text-xs sm:text-sm mt-1 line-clamp-2 break-words">
                      {feature.description}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-1 sm:gap-2 sm:ml-4 self-start">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEditFeature(feature)}
                    className="border-red/30 text-red hover:bg-red/10 text-xs px-2 py-1"
                  >
                    <Edit3 className="w-3 h-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDeleteFeature(feature.id)}
                    className="border-red/30 text-red hover:bg-red/10 text-xs px-2 py-1"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderSkillsEditor = () => (
    <div className="space-y-6">
      {/* Skills Header Content */}
      <div className="bg-dark-bg/50 rounded-lg border border-red/20 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Skills Header</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Section Title
            </label>
            <Input
              value={skillsContent?.title || "Professional Skills"}
              onChange={(e) =>
                setSkillsContent({ ...skillsContent, title: e.target.value })
              }
              className="bg-dark-bg border-red/30 text-white"
              disabled={!isEditing}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Description
            </label>
            <Textarea
              value={skillsContent?.description || ""}
              onChange={(e) =>
                setSkillsContent({ ...skillsContent, description: e.target.value })
              }
              className="bg-dark-bg border-red/30 text-white min-h-[100px]"
              disabled={!isEditing}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Highlighted Words (comma-separated)
            </label>
            <Input
              value={skillsContent?.highlightedWords?.join(", ") || ""}
              onChange={(e) =>
                setSkillsContent({
                  ...skillsContent,
                  highlightedWords: e.target.value
                    .split(",")
                    .map((word) => word.trim())
                    .filter((word) => word),
                })
              }
              className="bg-dark-bg border-red/30 text-white"
              disabled={!isEditing}
              placeholder="e.g., technical, expertise, technologies, innovation"
            />
            <p className="text-xs text-gray-400 mt-1">
              Words that will be highlighted in red in the description
            </p>
          </div>
        </div>
      </div>

      {/* Skills Management */}
      <div className="bg-dark-bg/50 rounded-lg border border-red/20 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Skills Management</h3>
          {isEditing && (
            <Button
              onClick={() => setSkills([...skills, { name: "", percentage: 0 }])}
              className="bg-red hover:bg-red/80"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Skill
            </Button>
          )}
        </div>

        <div className="space-y-4">
          {skills.map((skill, index) => (
            <div
              key={index}
              className="flex items-center gap-4 p-4 bg-dark-bg/50 rounded-lg border border-red/20"
            >
              <div className="flex-1">
                <Input
                  value={skill.name}
                  onChange={(e) => {
                    const newSkills = [...skills];
                    newSkills[index].name = e.target.value;
                    setSkills(newSkills);
                  }}
                  placeholder="Skill name"
                  className="bg-dark-bg border-red/30 text-white mb-2"
                  disabled={!isEditing}
                />
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    value={skill.percentage}
                    onChange={(e) => {
                      const newSkills = [...skills];
                      newSkills[index].percentage = parseInt(e.target.value) || 0;
                      setSkills(newSkills);
                    }}
                    className="bg-dark-bg border-red/30 text-white w-20"
                    disabled={!isEditing}
                  />
                  <span className="text-gray-400">%</span>
                </div>
              </div>
              {isEditing && (
                <Button
                  onClick={() => setSkills(skills.filter((_, i) => i !== index))}
                  variant="destructive"
                  size="sm"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Feature management functions
  const handleFeatureSubmit = async (e) => {
    e.preventDefault();
    try {
      const newFeature = {
        id: editingFeature?.id || Date.now(),
        ...featureFormData,
      };

      let updatedFeatures;
      if (editingFeature) {
        updatedFeatures = aboutContent.features.map(feature =>
          feature.id === editingFeature.id ? newFeature : feature
        );
      } else {
        updatedFeatures = [...aboutContent.features, newFeature];
      }

      const updatedAboutContent = {
        ...aboutContent,
        features: updatedFeatures
      };

      setAboutContent(updatedAboutContent);

      // Immediately save to database
      await updateContent({
        section: "about",
        content: updatedAboutContent
      });

      setShowFeatureForm(false);
      setEditingFeature(null);
      setFeatureFormData({
        title: "",
        description: "",
        icon: "",
      });
      toast.success("Feature saved successfully!");
    } catch (error) {
      console.error("Error saving feature:", error);
      toast.error("Failed to save feature");
    }
  };

  const handleEditFeature = (feature) => {
    setEditingFeature(feature);
    setFeatureFormData({
      title: feature.title || "",
      description: feature.description || "",
      icon: feature.icon || "",
    });
    setShowFeatureForm(true);
  };

  const handleDeleteFeature = async (id) => {
    if (window.confirm("Are you sure you want to delete this feature?")) {
      try {
        const updatedFeatures = aboutContent.features.filter(feature => feature.id !== id);
        const updatedAboutContent = {
          ...aboutContent,
          features: updatedFeatures
        };

        setAboutContent(updatedAboutContent);

        // Immediately save to database
        await updateContent({
          section: "about",
          content: updatedAboutContent
        });

        toast.success("Feature deleted successfully!");
      } catch (error) {
        console.error("Error deleting feature:", error);
        toast.error("Failed to delete feature");
      }
    }
  };

  // Resume management functions
  const handleResumeSubmit = async (e) => {
    e.preventDefault();
    try {
      const newItem = {
        id: editingResumeItem?.id || Date.now(),
        ...resumeFormData,
      };

      let updatedEducation = education;
      let updatedExperience = experience;

      if (resumeFormType === "education") {
        if (editingResumeItem) {
          updatedEducation = education.map(item =>
            item.id === editingResumeItem.id ? newItem : item
          );
        } else {
          updatedEducation = [...education, newItem];
        }
        setEducation(updatedEducation);
      } else {
        if (editingResumeItem) {
          updatedExperience = experience.map(item =>
            item.id === editingResumeItem.id ? newItem : item
          );
        } else {
          updatedExperience = [...experience, newItem];
        }
        setExperience(updatedExperience);
      }

      // Immediately save to database
      await updateContent({
        section: "resume",
        content: {
          ...resumeContent,
          education: updatedEducation,
          experience: updatedExperience
        }
      });

      setShowResumeForm(false);
      setEditingResumeItem(null);
      setResumeFormData({
        degree: "",
        institution: "",
        position: "",
        company: "",
        period: "",
        description: "",
      });
      toast.success(`${resumeFormType === "education" ? "Education" : "Experience"} item saved successfully!`);
    } catch (error) {
      console.error("Error saving resume item:", error);
      toast.error("Failed to save resume item");
    }
  };

  const handleEditResumeItem = (item, type) => {
    setEditingResumeItem(item);
    setResumeFormType(type);
    setResumeFormData({
      degree: item.degree || "",
      institution: item.institution || "",
      position: item.position || "",
      company: item.company || "",
      period: item.period || "",
      description: item.description || "",
    });
    setShowResumeForm(true);
  };

  const handleDeleteResumeItem = async (id, type) => {
    if (window.confirm(`Are you sure you want to delete this ${type} item?`)) {
      try {
        let updatedEducation = education;
        let updatedExperience = experience;

        if (type === "education") {
          updatedEducation = education.filter(item => item.id !== id);
          setEducation(updatedEducation);
        } else {
          updatedExperience = experience.filter(item => item.id !== id);
          setExperience(updatedExperience);
        }

        // Immediately save to database
        await updateContent({
          section: "resume",
          content: {
            ...resumeContent,
            education: updatedEducation,
            experience: updatedExperience
          }
        });

        toast.success(`${type === "education" ? "Education" : "Experience"} item deleted successfully!`);
      } catch (error) {
        console.error("Error deleting resume item:", error);
        toast.error("Failed to delete resume item");
      }
    }
  };

  const renderResumeEditor = () => (
    <div className="space-y-4 sm:space-y-6">
      {/* Resume Header Content */}
      <div className="bg-dark-bg/50 rounded-lg border border-red/20 p-4 sm:p-6">
        <h3 className="text-base sm:text-lg font-semibold text-white mb-4">Resume Header</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Section Title
            </label>
            <Input
              value={resumeContent.title}
              onChange={(e) =>
                setResumeContent({ ...resumeContent, title: e.target.value })
              }
              className="bg-dark-bg border-red/30 text-white"
              disabled={!isEditing}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Description
            </label>
            <Textarea
              value={resumeContent.description}
              onChange={(e) =>
                setResumeContent({ ...resumeContent, description: e.target.value })
              }
              className="bg-dark-bg border-red/30 text-white min-h-[100px]"
              disabled={!isEditing}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Highlighted Words (comma-separated)
            </label>
            <Input
              value={resumeContent.highlightedWords?.join(", ") || ""}
              onChange={(e) =>
                setResumeContent({
                  ...resumeContent,
                  highlightedWords: e.target.value
                    .split(",")
                    .map((word) => word.trim())
                    .filter((word) => word),
                })
              }
              className="bg-dark-bg border-red/30 text-white"
              disabled={!isEditing}
              placeholder="e.g., software, development, expertise, technology"
            />
            <p className="text-xs text-gray-400 mt-1">
              Words that will be highlighted in red in the main resume description
            </p>
          </div>
        </div>
      </div>

      {/* Education Section */}
      <div className="bg-dark-bg/50 rounded-lg border border-red/20 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4 mb-4">
          <h3 className="text-base sm:text-lg font-semibold text-white">Education</h3>
          <Button
            onClick={() => {
              setEditingResumeItem(null);
              setResumeFormType("education");
              setResumeFormData({
                degree: "",
                institution: "",
                position: "",
                company: "",
                period: "",
                description: "",
              });
              setShowResumeForm(true);
            }}
            size="sm"
            className="bg-red hover:bg-red/80 text-xs sm:text-sm w-full sm:w-auto"
          >
            <Plus className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            Add Education
          </Button>
        </div>

        <div className="space-y-3 sm:space-y-4">
          {education.map((item) => (
            <div
              key={item.id}
              className="bg-dark-bg/30 rounded-lg border border-red/10 p-3 sm:p-4"
            >
              <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-3">
                <div className="flex-1 min-w-0">
                  <h4 className="text-white font-semibold text-sm sm:text-base break-words">{item.degree}</h4>
                  <p className="text-gray-400 text-xs sm:text-sm break-words">{item.institution}</p>
                  <p className="text-red text-xs sm:text-sm">{item.period}</p>
                  <p className="text-gray-300 text-xs sm:text-sm mt-2 line-clamp-2 break-words">
                    {item.description}
                  </p>
                </div>
                <div className="flex items-center gap-1 sm:gap-2 sm:ml-4 self-start">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEditResumeItem(item, "education")}
                    className="border-red/30 text-red hover:bg-red/10 text-xs px-2 py-1"
                  >
                    <Edit3 className="w-3 h-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDeleteResumeItem(item.id, "education")}
                    className="border-red/30 text-red hover:bg-red/10 text-xs px-2 py-1"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Experience Section */}
      <div className="bg-dark-bg/50 rounded-lg border border-red/20 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4 mb-4">
          <h3 className="text-base sm:text-lg font-semibold text-white">Experience</h3>
          <Button
            onClick={() => {
              setEditingResumeItem(null);
              setResumeFormType("experience");
              setResumeFormData({
                degree: "",
                institution: "",
                position: "",
                company: "",
                period: "",
                description: "",
              });
              setShowResumeForm(true);
            }}
            size="sm"
            className="bg-red hover:bg-red/80 text-xs sm:text-sm w-full sm:w-auto"
          >
            <Plus className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            Add Experience
          </Button>
        </div>

        <div className="space-y-3 sm:space-y-4">
          {experience.map((item) => (
            <div
              key={item.id}
              className="bg-dark-bg/30 rounded-lg border border-red/10 p-3 sm:p-4"
            >
              <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-3">
                <div className="flex-1 min-w-0">
                  <h4 className="text-white font-semibold text-sm sm:text-base break-words">{item.position}</h4>
                  <p className="text-gray-400 text-xs sm:text-sm break-words">{item.company}</p>
                  <p className="text-red text-xs sm:text-sm">{item.period}</p>
                  <p className="text-gray-300 text-xs sm:text-sm mt-2 line-clamp-2 break-words">
                    {item.description}
                  </p>
                </div>
                <div className="flex items-center gap-1 sm:gap-2 sm:ml-4 self-start">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEditResumeItem(item, "experience")}
                    className="border-red/30 text-red hover:bg-red/10 text-xs px-2 py-1"
                  >
                    <Edit3 className="w-3 h-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDeleteResumeItem(item.id, "experience")}
                    className="border-red/30 text-red hover:bg-red/10 text-xs px-2 py-1"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Category management functions
  const handleCategorySubmit = async (e) => {
    e.preventDefault();
    try {
      const newCategory = {
        ...categoryFormData,
        display_order: editingCategory?.display_order || dbCategories.length,
      };

      if (editingCategory) {
        await updateCategory({ id: editingCategory.id, ...newCategory });
      } else {
        await createCategory(newCategory);
      }

      setShowCategoryForm(false);
      setEditingCategory(null);
      setCategoryFormData({
        name: "",
        description: "",
        color: "#ef4444",
        display_order: 0,
        is_active: true,
      });
    } catch (error) {
      console.error("Error saving category:", error);
    }
  };

  const handleEditCategory = (category) => {
    setEditingCategory(category);
    setCategoryFormData({
      name: category.name || "",
      description: category.description || "",
      color: category.color || "#ef4444",
      display_order: category.display_order || 0,
      is_active: category.is_active !== false,
    });
    setShowCategoryForm(true);
  };

  const handleDeleteCategory = async (id) => {
    if (window.confirm("Are you sure you want to delete this category?")) {
      await deleteCategory(id);
    }
  };

  // Portfolio management functions
  const handlePortfolioSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingPortfolioItem) {
        await updateItem({
          id: editingPortfolioItem.id,
          ...portfolioFormData,
          technologies: portfolioFormData.technologies.filter(tech => tech.trim()),
        });
        toast.success("Portfolio item updated successfully!");
      } else {
        await createItem({
          ...portfolioFormData,
          technologies: portfolioFormData.technologies.filter(tech => tech.trim()),
        });
        toast.success("Portfolio item created successfully!");
      }
      setShowPortfolioForm(false);
      setEditingPortfolioItem(null);
      setPortfolioFormData({
        title: "",
        description: "",
        category: "",
        image_url: "",
        technologies: [],
        live_url: "",
        github_url: "",
        featured: false,
      });
    } catch (error) {
      console.error("Error saving portfolio item:", error);
      toast.error("Failed to save portfolio item");
    }
  };

  const handleEditPortfolioItem = (item) => {
    setEditingPortfolioItem(item);
    setPortfolioFormData({
      title: item.title,
      description: item.description,
      category: item.category,
      image_url: item.image_url || "",
      technologies: item.technologies || [],
      live_url: item.live_url || "",
      github_url: item.github_url || "",
      featured: item.featured,
    });
    setShowPortfolioForm(true);
  };

  const handleDeletePortfolioItem = async (id) => {
    if (window.confirm("Are you sure you want to delete this portfolio item?")) {
      try {
        await deleteItem(id);
        toast.success("Portfolio item deleted successfully!");
      } catch (error) {
        console.error("Error deleting portfolio item:", error);
        toast.error("Failed to delete portfolio item");
      }
    }
  };

  const renderPortfolioEditor = () => (
    <div className="space-y-4 sm:space-y-6">
      {/* Portfolio Header Content */}
      <div className="bg-dark-bg/50 rounded-lg border border-red/20 p-4 sm:p-6">
        <h3 className="text-base sm:text-lg font-semibold text-white mb-4">Portfolio Header</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Section Title
            </label>
            <Input
              value={portfolioContent.title}
              onChange={(e) =>
                setPortfolioContent({ ...portfolioContent, title: e.target.value })
              }
              className="bg-dark-bg border-red/30 text-white"
              disabled={!isEditing}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Description
            </label>
            <Textarea
              value={portfolioContent.description}
              onChange={(e) =>
                setPortfolioContent({ ...portfolioContent, description: e.target.value })
              }
              className="bg-dark-bg border-red/30 text-white min-h-[100px]"
              disabled={!isEditing}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Highlighted Words (comma-separated)
            </label>
            <Input
              value={portfolioContent.highlightedWords?.join(", ") || ""}
              onChange={(e) =>
                setPortfolioContent({
                  ...portfolioContent,
                  highlightedWords: e.target.value
                    .split(",")
                    .map((word) => word.trim())
                    .filter((word) => word),
                })
              }
              className="bg-dark-bg border-red/30 text-white"
              disabled={!isEditing}
              placeholder="e.g., creative, innovative, solutions, digital"
            />
            <p className="text-xs text-gray-400 mt-1">
              Words that will be highlighted in red in the portfolio description
            </p>
          </div>
        </div>
      </div>

      {/* Category Management */}
      <div className="bg-dark-bg/50 rounded-lg border border-red/20 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4 mb-4">
          <h3 className="text-base sm:text-lg font-semibold text-white">Portfolio Categories</h3>
          <Button
            onClick={() => {
              setEditingCategory(null);
              setCategoryFormData({
                name: "",
                description: "",
                color: "#ef4444",
                display_order: 0,
                is_active: true,
              });
              setShowCategoryForm(true);
            }}
            size="sm"
            className="bg-red hover:bg-red/80 text-xs sm:text-sm w-full sm:w-auto"
          >
            <Plus className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            Add Category
          </Button>
        </div>

        <div className="space-y-3 sm:space-y-4">
          {dbCategories?.filter(cat => cat.name !== "All").map((category) => (
            <div
              key={category.id}
              className="bg-dark-bg/30 rounded-lg border border-red/10 p-3 sm:p-4"
            >
              <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-3">
                <div className="flex items-start gap-2 sm:gap-3 flex-1 min-w-0">
                  <div
                    className="w-3 h-3 sm:w-4 sm:h-4 rounded-full mt-1 flex-shrink-0"
                    style={{ backgroundColor: category.color }}
                  ></div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-white font-semibold text-sm sm:text-base break-words">{category.name}</h4>
                    <p className="text-gray-300 text-xs sm:text-sm mt-1 break-words">
                      {category.description}
                    </p>
                    <p className="text-gray-500 text-xs mt-1">
                      Order: {category.display_order}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-1 sm:gap-2 sm:ml-4 self-start">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEditCategory(category)}
                    className="border-red/30 text-red hover:bg-red/10 text-xs px-2 py-1"
                  >
                    <Edit3 className="w-3 h-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDeleteCategory(category.id)}
                    className="border-red/30 text-red hover:bg-red/10 text-xs px-2 py-1"
                    disabled={isDeletingCategory}
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Portfolio Items Management */}
      <div className="bg-dark-bg/50 rounded-lg border border-red/20 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4 mb-4">
          <h3 className="text-base sm:text-lg font-semibold text-white">Portfolio Items</h3>
          <Button
            onClick={() => {
              setEditingPortfolioItem(null);
              setPortfolioFormData({
                title: "",
                description: "",
                category: "",
                image_url: "",
                technologies: [],
                live_url: "",
                github_url: "",
                featured: false,
              });
              setShowPortfolioForm(true);
            }}
            size="sm"
            className="bg-red hover:bg-red/80 text-xs sm:text-sm w-full sm:w-auto"
          >
            <Plus className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            Add Portfolio Item
          </Button>
        </div>

      {/* Portfolio Items Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {dbPortfolioItems?.map((item) => (
          <div
            key={item.id}
            className="bg-dark-bg/50 rounded-lg border border-red/20 overflow-hidden"
          >
            <div className="aspect-video bg-gray-800 relative">
              {item.image_url ? (
                <img
                  src={item.image_url}
                  alt={item.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <Image className="w-6 h-6 sm:w-8 sm:h-8 text-gray-500" />
                </div>
              )}
              {item.featured && (
                <div className="absolute top-1 sm:top-2 right-1 sm:right-2 bg-red text-white px-1.5 sm:px-2 py-0.5 sm:py-1 rounded text-xs">
                  Featured
                </div>
              )}
            </div>
            <div className="p-3 sm:p-4">
              <h4 className="text-white font-semibold text-sm sm:text-base mb-2 break-words">{item.title}</h4>
              <p className="text-gray-400 text-xs sm:text-sm mb-2 line-clamp-2 break-words">
                {item.description}
              </p>
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 text-xs text-gray-500 mb-3">
                <span className="bg-red/20 text-red px-2 py-1 rounded text-xs truncate">
                  {item.category}
                </span>
                <span className="text-xs">{new Date(item.created_at).toLocaleDateString()}</span>
              </div>
              <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleEditPortfolioItem(item)}
                  className="border-red/30 text-red hover:bg-red/10 text-xs flex-1 sm:flex-none"
                >
                  <Edit3 className="w-3 h-3 mr-1" />
                  Edit
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDeletePortfolioItem(item.id)}
                  className="border-red/30 text-red hover:bg-red/10 text-xs flex-1 sm:flex-none"
                  disabled={isDeleting}
                >
                  <Trash2 className="w-3 h-3 mr-1" />
                  Delete
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {(!dbPortfolioItems || dbPortfolioItems.length === 0) && (
        <div className="text-center py-12">
          <Briefcase className="w-12 h-12 text-red/50 mx-auto mb-4" />
          <p className="text-gray-400 mb-4">No portfolio items yet</p>
          <Button
            onClick={() => setShowPortfolioForm(true)}
            className="bg-red hover:bg-red/80"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Your First Portfolio Item
          </Button>
        </div>
      )}
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case "hero":
        return renderHeroEditor();
      case "about":
        return renderAboutEditor();
      case "resume":
        return renderResumeEditor();
      case "skills":
        return renderSkillsEditor();
      case "portfolio":
        return renderPortfolioEditor();
      case "testimonials":
        return (
          <div className="text-center py-12">
            <MessageSquare className="w-12 h-12 text-red/50 mx-auto mb-4" />
            <p className="text-gray-400 mb-4">
              Testimonials management coming soon...
            </p>
            <Button className="bg-red hover:bg-red/80">
              <Plus className="w-4 h-4 mr-2" />
              Add Testimonial
            </Button>
          </div>
        );
      case "contact":
        return (
          <div className="space-y-8">
            {/* Contact Information Header */}
            <div className="bg-dark-bg/50 rounded-lg border border-red/20 p-6">
              <h3 className="text-xl font-semibold text-white mb-2 flex items-center gap-2">
                <MessageSquare className="w-5 h-5 text-red" />
                Contact Information
              </h3>
              <p className="text-gray-400 mb-6">
                Manage your contact details and social media links
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Contact Info */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-white border-b border-red/20 pb-2">
                    Basic Information
                  </h4>

                  <div className="space-y-2">
                    <Label htmlFor="phone" className="text-white">Phone Number</Label>
                    <Input
                      id="phone"
                      type="tel"
                      placeholder="+****************"
                      value={contactInfo.phone}
                      onChange={(e) =>
                        setContactInfo((prev) => ({ ...prev, phone: e.target.value }))
                      }
                      className="bg-dark-bg border-red/30 text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-white">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={contactInfo.email}
                      onChange={(e) =>
                        setContactInfo((prev) => ({ ...prev, email: e.target.value }))
                      }
                      className="bg-dark-bg border-red/30 text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address" className="text-white">Address/Location</Label>
                    <Input
                      id="address"
                      type="text"
                      placeholder="City, Country"
                      value={contactInfo.address}
                      onChange={(e) =>
                        setContactInfo((prev) => ({ ...prev, address: e.target.value }))
                      }
                      className="bg-dark-bg border-red/30 text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="availability" className="text-white">Availability Status</Label>
                    <Input
                      id="availability"
                      type="text"
                      placeholder="Available for freelance work"
                      value={contactInfo.availability}
                      onChange={(e) =>
                        setContactInfo((prev) => ({ ...prev, availability: e.target.value }))
                      }
                      className="bg-dark-bg border-red/30 text-white"
                    />
                  </div>
                </div>

                {/* Social Media Links */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-white border-b border-red/20 pb-2">
                    Social Media Links
                  </h4>

                  {/* LinkedIn */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="linkedin" className="text-white flex items-center gap-2">
                        <Linkedin className="w-4 h-4 text-blue-400" />
                        LinkedIn Profile
                      </Label>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-400">Visible</span>
                        <button
                          type="button"
                          onClick={() =>
                            setContactInfo((prev) => ({
                              ...prev,
                              socialVisibility: {
                                ...prev.socialVisibility,
                                linkedin: !prev.socialVisibility?.linkedin
                              }
                            }))
                          }
                          className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                            contactInfo.socialVisibility?.linkedin ? 'bg-red' : 'bg-gray-600'
                          }`}
                        >
                          <span
                            className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                              contactInfo.socialVisibility?.linkedin ? 'translate-x-5' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                    <Input
                      id="linkedin"
                      type="url"
                      placeholder="https://linkedin.com/in/yourprofile"
                      value={contactInfo.socialLinks?.linkedin || ""}
                      onChange={(e) =>
                        setContactInfo((prev) => ({
                          ...prev,
                          socialLinks: { ...prev.socialLinks, linkedin: e.target.value }
                        }))
                      }
                      className="bg-dark-bg border-red/30 text-white"
                    />
                  </div>

                  {/* GitHub */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="github" className="text-white flex items-center gap-2">
                        <Github className="w-4 h-4 text-gray-400" />
                        GitHub Profile
                      </Label>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-400">Visible</span>
                        <button
                          type="button"
                          onClick={() =>
                            setContactInfo((prev) => ({
                              ...prev,
                              socialVisibility: {
                                ...prev.socialVisibility,
                                github: !prev.socialVisibility?.github
                              }
                            }))
                          }
                          className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                            contactInfo.socialVisibility?.github ? 'bg-red' : 'bg-gray-600'
                          }`}
                        >
                          <span
                            className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                              contactInfo.socialVisibility?.github ? 'translate-x-5' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                    <Input
                      id="github"
                      type="url"
                      placeholder="https://github.com/yourusername"
                      value={contactInfo.socialLinks?.github || ""}
                      onChange={(e) =>
                        setContactInfo((prev) => ({
                          ...prev,
                          socialLinks: { ...prev.socialLinks, github: e.target.value }
                        }))
                      }
                      className="bg-dark-bg border-red/30 text-white"
                    />
                  </div>

                  {/* Twitter */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="twitter" className="text-white flex items-center gap-2">
                        <Twitter className="w-4 h-4 text-blue-400" />
                        Twitter/X Profile
                      </Label>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-400">Visible</span>
                        <button
                          type="button"
                          onClick={() =>
                            setContactInfo((prev) => ({
                              ...prev,
                              socialVisibility: {
                                ...prev.socialVisibility,
                                twitter: !prev.socialVisibility?.twitter
                              }
                            }))
                          }
                          className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                            contactInfo.socialVisibility?.twitter ? 'bg-red' : 'bg-gray-600'
                          }`}
                        >
                          <span
                            className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                              contactInfo.socialVisibility?.twitter ? 'translate-x-5' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                    <Input
                      id="twitter"
                      type="url"
                      placeholder="https://twitter.com/yourusername"
                      value={contactInfo.socialLinks?.twitter || ""}
                      onChange={(e) =>
                        setContactInfo((prev) => ({
                          ...prev,
                          socialLinks: { ...prev.socialLinks, twitter: e.target.value }
                        }))
                      }
                      className="bg-dark-bg border-red/30 text-white"
                    />
                  </div>

                  {/* Instagram */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="instagram" className="text-white flex items-center gap-2">
                        <Instagram className="w-4 h-4 text-pink-400" />
                        Instagram Profile
                      </Label>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-400">Visible</span>
                        <button
                          type="button"
                          onClick={() =>
                            setContactInfo((prev) => ({
                              ...prev,
                              socialVisibility: {
                                ...prev.socialVisibility,
                                instagram: !prev.socialVisibility?.instagram
                              }
                            }))
                          }
                          className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                            contactInfo.socialVisibility?.instagram ? 'bg-red' : 'bg-gray-600'
                          }`}
                        >
                          <span
                            className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                              contactInfo.socialVisibility?.instagram ? 'translate-x-5' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                    <Input
                      id="instagram"
                      type="url"
                      placeholder="https://instagram.com/yourusername"
                      value={contactInfo.socialLinks?.instagram || ""}
                      onChange={(e) =>
                        setContactInfo((prev) => ({
                          ...prev,
                          socialLinks: { ...prev.socialLinks, instagram: e.target.value }
                        }))
                      }
                      className="bg-dark-bg border-red/30 text-white"
                    />
                  </div>

                  {/* Facebook */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="facebook" className="text-white flex items-center gap-2">
                        <Facebook className="w-4 h-4 text-blue-500" />
                        Facebook Profile
                      </Label>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-400">Visible</span>
                        <button
                          type="button"
                          onClick={() =>
                            setContactInfo((prev) => ({
                              ...prev,
                              socialVisibility: {
                                ...prev.socialVisibility,
                                facebook: !prev.socialVisibility?.facebook
                              }
                            }))
                          }
                          className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                            contactInfo.socialVisibility?.facebook ? 'bg-red' : 'bg-gray-600'
                          }`}
                        >
                          <span
                            className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                              contactInfo.socialVisibility?.facebook ? 'translate-x-5' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                    <Input
                      id="facebook"
                      type="url"
                      placeholder="https://facebook.com/yourusername"
                      value={contactInfo.socialLinks?.facebook || ""}
                      onChange={(e) =>
                        setContactInfo((prev) => ({
                          ...prev,
                          socialLinks: { ...prev.socialLinks, facebook: e.target.value }
                        }))
                      }
                      className="bg-dark-bg border-red/30 text-white"
                    />
                  </div>
                </div>
              </div>

              {/* Save Button */}
              <div className="flex justify-end mt-6 pt-4 border-t border-red/20">
                <Button
                  onClick={() => handleSave("contact")}
                  disabled={isUpdating}
                  className="bg-red hover:bg-red/80 text-white"
                >
                  {isUpdating ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Save Contact Info
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Preview Section */}
            <div className="bg-dark-bg/30 rounded-lg border border-red/10 p-6">
              <h4 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
                <Eye className="w-5 h-5 text-red" />
                Contact Info Preview
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-gray-300">
                    <Phone className="w-4 h-4 text-red" />
                    <span>{contactInfo.phone || "No phone number set"}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-300">
                    <Mail className="w-4 h-4 text-red" />
                    <span>{contactInfo.email || "No email set"}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-300">
                    <MapPin className="w-4 h-4 text-red" />
                    <span>{contactInfo.address || "No address set"}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-300">
                    <Clock className="w-4 h-4 text-red" />
                    <span>{contactInfo.availability || "No availability status set"}</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <p className="text-white font-medium mb-2">Social Links:</p>
                  {contactInfo.socialLinks?.linkedin && contactInfo.socialVisibility?.linkedin && (
                    <div className="flex items-center gap-2 text-gray-300">
                      <Linkedin className="w-4 h-4 text-blue-400" />
                      <span className="truncate">{contactInfo.socialLinks.linkedin}</span>
                      <span className="text-xs text-green-400">(Visible)</span>
                    </div>
                  )}
                  {contactInfo.socialLinks?.github && contactInfo.socialVisibility?.github && (
                    <div className="flex items-center gap-2 text-gray-300">
                      <Github className="w-4 h-4 text-gray-400" />
                      <span className="truncate">{contactInfo.socialLinks.github}</span>
                      <span className="text-xs text-green-400">(Visible)</span>
                    </div>
                  )}
                  {contactInfo.socialLinks?.twitter && contactInfo.socialVisibility?.twitter && (
                    <div className="flex items-center gap-2 text-gray-300">
                      <Twitter className="w-4 h-4 text-blue-400" />
                      <span className="truncate">{contactInfo.socialLinks.twitter}</span>
                      <span className="text-xs text-green-400">(Visible)</span>
                    </div>
                  )}
                  {contactInfo.socialLinks?.instagram && contactInfo.socialVisibility?.instagram && (
                    <div className="flex items-center gap-2 text-gray-300">
                      <Instagram className="w-4 h-4 text-pink-400" />
                      <span className="truncate">{contactInfo.socialLinks.instagram}</span>
                      <span className="text-xs text-green-400">(Visible)</span>
                    </div>
                  )}
                  {contactInfo.socialLinks?.facebook && contactInfo.socialVisibility?.facebook && (
                    <div className="flex items-center gap-2 text-gray-300">
                      <Facebook className="w-4 h-4 text-blue-500" />
                      <span className="truncate">{contactInfo.socialLinks.facebook}</span>
                      <span className="text-xs text-green-400">(Visible)</span>
                    </div>
                  )}
                  {(!contactInfo.socialLinks?.linkedin || !contactInfo.socialVisibility?.linkedin) &&
                   (!contactInfo.socialLinks?.github || !contactInfo.socialVisibility?.github) &&
                   (!contactInfo.socialLinks?.twitter || !contactInfo.socialVisibility?.twitter) &&
                   (!contactInfo.socialLinks?.instagram || !contactInfo.socialVisibility?.instagram) &&
                   (!contactInfo.socialLinks?.facebook || !contactInfo.socialVisibility?.facebook) && (
                    <span className="text-gray-500">No visible social links</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="text-center py-12">
            <FileText className="w-12 h-12 text-red/50 mx-auto mb-4" />
            <p className="text-gray-400">
              Content editor for {activeTab} section coming soon...
            </p>
          </div>
        );
    }
  };

  return (
    <AdminLayout title="Content Management">
      <div className="space-y-4 sm:space-y-6 max-w-full overflow-hidden">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h2 className="text-xl sm:text-2xl font-bold text-white">
              Content Management
            </h2>
            <p className="text-gray-400 text-sm sm:text-base">
              Edit and manage your portfolio content
            </p>
          </div>
          <div className="flex items-center gap-2 sm:gap-3">
            <Button
              onClick={() => window.open("/", "_blank")}
              variant="outline"
              size="sm"
              className="border-red/30 text-red hover:bg-red/10 text-xs sm:text-sm"
            >
              <Eye className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">Preview Site</span>
              <span className="sm:hidden">Preview</span>
            </Button>
            {isEditing ? (
              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  onClick={handleSave}
                  disabled={isUpdating || saveStatus === "saving"}
                  size="sm"
                  className={`${
                    saveStatus === "saved"
                      ? "bg-green-600 hover:bg-green-700"
                      : saveStatus === "error"
                      ? "bg-red-600 hover:bg-red-700"
                      : "bg-green-600 hover:bg-green-700"
                  } text-xs sm:text-sm`}
                >
                  {isUpdating || saveStatus === "saving" ? (
                    <div className="flex items-center gap-1 sm:gap-2">
                      <div className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      <span className="hidden sm:inline">Saving...</span>
                      <span className="sm:hidden">Save...</span>
                    </div>
                  ) : saveStatus === "saved" ? (
                    <div className="flex items-center gap-1 sm:gap-2">
                      <Check className="w-3 h-3 sm:w-4 sm:h-4" />
                      Saved!
                    </div>
                  ) : saveStatus === "error" ? (
                    <div className="flex items-center gap-1 sm:gap-2">
                      <X className="w-3 h-3 sm:w-4 sm:h-4" />
                      Error
                    </div>
                  ) : (
                    <>
                      <Save className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                      <span className="hidden sm:inline">Save Changes</span>
                      <span className="sm:hidden">Save</span>
                    </>
                  )}
                </Button>
                <Button
                  onClick={() => setIsEditing(false)}
                  variant="outline"
                  size="sm"
                  className="border-gray-600 text-gray-400 text-xs sm:text-sm"
                >
                  Cancel
                </Button>
              </div>
            ) : (
              <Button
                onClick={() => setIsEditing(true)}
                size="sm"
                className="bg-red hover:bg-red/80 text-xs sm:text-sm"
              >
                <Edit3 className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Edit Content</span>
                <span className="sm:hidden">Edit</span>
              </Button>
            )}
          </div>
        </div>

        {/* Content Tabs */}
        <div className="bg-dark-lighter border border-red/20 rounded-xl overflow-hidden">
          {/* Mobile Tab Selector */}
          <div className="block sm:hidden border-b border-red/20 p-4">
            <select
              value={activeTab}
              onChange={(e) => setActiveTab(e.target.value)}
              className="w-full bg-dark-bg border border-red/20 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-red/50"
            >
              {contentSections.map((section) => (
                <option key={section.id} value={section.id}>
                  {section.label}
                </option>
              ))}
            </select>
          </div>

          {/* Desktop Tabs */}
          <div className="hidden sm:flex overflow-x-auto border-b border-red/20">
            {contentSections.map((section) => {
              const Icon = section.icon;
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveTab(section.id)}
                  className={`flex items-center gap-2 px-4 lg:px-6 py-3 lg:py-4 whitespace-nowrap transition-all text-sm lg:text-base ${
                    activeTab === section.id
                      ? "bg-red/20 text-red border-b-2 border-red"
                      : "text-gray-400 hover:text-white hover:bg-red/10"
                  }`}
                >
                  <Icon className="w-3 h-3 lg:w-4 lg:h-4" />
                  <span className="hidden md:inline">{section.label}</span>
                  <span className="md:hidden">{section.label.split(' ')[0]}</span>
                </button>
              );
            })}
          </div>

          <div className="p-3 sm:p-4 lg:p-6">{renderContent()}</div>
        </div>
      </div>

      {/* Portfolio Form Modal */}
      {showPortfolioForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-2 sm:p-4">
          <div className="bg-dark-lighter border border-red/20 rounded-xl max-w-2xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
            <div className="p-4 sm:p-6">
              <div className="flex items-center justify-between mb-4 sm:mb-6">
                <h3 className="text-lg sm:text-xl font-bold text-white">
                  {editingPortfolioItem ? "Edit Portfolio Item" : "Add Portfolio Item"}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowPortfolioForm(false);
                    setEditingPortfolioItem(null);
                  }}
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </Button>
              </div>

              <form onSubmit={handlePortfolioSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Title *
                    </label>
                    <Input
                      value={portfolioFormData.title}
                      onChange={(e) =>
                        setPortfolioFormData({
                          ...portfolioFormData,
                          title: e.target.value,
                        })
                      }
                      className="bg-dark-bg border-red/30 text-white"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Category *
                    </label>
                    <select
                      value={portfolioFormData.category}
                      onChange={(e) =>
                        setPortfolioFormData({
                          ...portfolioFormData,
                          category: e.target.value,
                        })
                      }
                      className="bg-dark-bg border border-red/30 text-white rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-red/50"
                      required
                    >
                      <option value="">Select a category</option>
                      {getCategoryNames().map((categoryName) => (
                        <option key={categoryName} value={categoryName}>
                          {categoryName}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Description *
                  </label>
                  <Textarea
                    value={portfolioFormData.description}
                    onChange={(e) =>
                      setPortfolioFormData({
                        ...portfolioFormData,
                        description: e.target.value,
                      })
                    }
                    className="bg-dark-bg border-red/30 text-white min-h-[100px]"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Image URL
                  </label>
                  <div className="space-y-3">
                    <Input
                      value={portfolioFormData.image_url}
                      onChange={(e) =>
                        setPortfolioFormData({
                          ...portfolioFormData,
                          image_url: e.target.value,
                        })
                      }
                      className="bg-dark-bg border-red/30 text-white"
                      placeholder="https://example.com/image.jpg"
                    />
                    <div className="flex items-center gap-3">
                      <Button
                        type="button"
                        variant="outline"
                        className="border-red/30 text-red hover:bg-red/10"
                        disabled={uploading}
                        onClick={() => {
                          const input = document.createElement("input");
                          input.type = "file";
                          input.accept = "image/*";
                          input.onchange = async (e) => {
                            const file = (e.target as HTMLInputElement).files?.[0];
                            if (file) {
                              try {
                                const url = await uploadFile(file, "portfolio-images");
                                setPortfolioFormData({
                                  ...portfolioFormData,
                                  image_url: url,
                                });
                                toast.success("Image uploaded successfully!");
                              } catch (error) {
                                toast.error("Failed to upload image");
                              }
                            }
                          };
                          input.click();
                        }}
                      >
                        {uploading ? (
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 border-2 border-red/30 border-t-red rounded-full animate-spin"></div>
                            Uploading...
                          </div>
                        ) : (
                          <>
                            <Upload className="w-4 h-4 mr-2" />
                            Upload Image
                          </>
                        )}
                      </Button>
                    </div>
                    {portfolioFormData.image_url && (
                      <div className="mt-3">
                        <img
                          src={portfolioFormData.image_url}
                          alt="Preview"
                          className="w-32 h-20 object-cover rounded border-2 border-red/30"
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Live URL
                    </label>
                    <Input
                      value={portfolioFormData.live_url}
                      onChange={(e) =>
                        setPortfolioFormData({
                          ...portfolioFormData,
                          live_url: e.target.value,
                        })
                      }
                      className="bg-dark-bg border-red/30 text-white"
                      placeholder="https://example.com"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      GitHub URL
                    </label>
                    <Input
                      value={portfolioFormData.github_url}
                      onChange={(e) =>
                        setPortfolioFormData({
                          ...portfolioFormData,
                          github_url: e.target.value,
                        })
                      }
                      className="bg-dark-bg border-red/30 text-white"
                      placeholder="https://github.com/username/repo"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Technologies (comma-separated)
                  </label>
                  <Input
                    value={portfolioFormData.technologies.join(", ")}
                    onChange={(e) =>
                      setPortfolioFormData({
                        ...portfolioFormData,
                        technologies: e.target.value
                          .split(",")
                          .map((tech) => tech.trim())
                          .filter((tech) => tech),
                      })
                    }
                    className="bg-dark-bg border-red/30 text-white"
                    placeholder="React, Node.js, MongoDB"
                  />
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="featured"
                    checked={portfolioFormData.featured}
                    onChange={(e) =>
                      setPortfolioFormData({
                        ...portfolioFormData,
                        featured: e.target.checked,
                      })
                    }
                    className="w-4 h-4 text-red bg-dark-bg border-red/30 rounded focus:ring-red focus:ring-2"
                  />
                  <label htmlFor="featured" className="text-sm text-gray-300">
                    Featured item (will be highlighted)
                  </label>
                </div>

                <div className="flex items-center gap-3 pt-4">
                  <Button
                    type="submit"
                    className="bg-red hover:bg-red/80"
                    disabled={isCreating || isUpdatingItem}
                  >
                    {isCreating || isUpdatingItem ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        {editingPortfolioItem ? "Updating..." : "Creating..."}
                      </div>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        {editingPortfolioItem ? "Update Item" : "Create Item"}
                      </>
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowPortfolioForm(false);
                      setEditingPortfolioItem(null);
                    }}
                    className="border-red/30 text-red hover:bg-red/10"
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Category Form Modal */}
      {showCategoryForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-dark-lighter border border-red/20 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white">
                  {editingCategory ? "Edit Category" : "Add Category"}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowCategoryForm(false);
                    setEditingCategory(null);
                  }}
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </Button>
              </div>

              <form onSubmit={handleCategorySubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Category Name
                  </label>
                  <Input
                    value={categoryFormData.name}
                    onChange={(e) =>
                      setCategoryFormData({ ...categoryFormData, name: e.target.value })
                    }
                    className="bg-dark-bg border-red/30 text-white"
                    placeholder="e.g., Web Development"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Description
                  </label>
                  <Textarea
                    value={categoryFormData.description}
                    onChange={(e) =>
                      setCategoryFormData({ ...categoryFormData, description: e.target.value })
                    }
                    className="bg-dark-bg border-red/30 text-white min-h-[100px]"
                    placeholder="Describe what this category includes..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Color
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={categoryFormData.color}
                      onChange={(e) =>
                        setCategoryFormData({ ...categoryFormData, color: e.target.value })
                      }
                      className="w-12 h-10 rounded border border-red/30 bg-dark-bg"
                    />
                    <Input
                      value={categoryFormData.color}
                      onChange={(e) =>
                        setCategoryFormData({ ...categoryFormData, color: e.target.value })
                      }
                      className="bg-dark-bg border-red/30 text-white flex-1"
                      placeholder="#ef4444"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Display Order
                  </label>
                  <Input
                    type="number"
                    value={categoryFormData.display_order}
                    onChange={(e) =>
                      setCategoryFormData({ ...categoryFormData, display_order: parseInt(e.target.value) || 0 })
                    }
                    className="bg-dark-bg border-red/30 text-white"
                    min="0"
                  />
                  <p className="text-xs text-gray-400 mt-1">
                    Lower numbers appear first in the category list
                  </p>
                </div>

                <div className="flex items-center gap-3 pt-4">
                  <Button
                    type="submit"
                    className="bg-red hover:bg-red/80 flex-1"
                    disabled={isCreatingCategory || isUpdatingCategory}
                  >
                    {isCreatingCategory || isUpdatingCategory ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        {editingCategory ? "Updating..." : "Creating..."}
                      </div>
                    ) : (
                      editingCategory ? "Update Category" : "Add Category"
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowCategoryForm(false);
                      setEditingCategory(null);
                    }}
                    className="border-red/30 text-red hover:bg-red/10"
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Feature Form Modal */}
      {showFeatureForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-dark-lighter border border-red/20 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white">
                  {editingFeature ? "Edit Feature" : "Add Feature"}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowFeatureForm(false);
                    setEditingFeature(null);
                  }}
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </Button>
              </div>

              <form onSubmit={handleFeatureSubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Feature Title
                  </label>
                  <Input
                    value={featureFormData.title}
                    onChange={(e) =>
                      setFeatureFormData({ ...featureFormData, title: e.target.value })
                    }
                    className="bg-dark-bg border-red/30 text-white"
                    placeholder="e.g., Business Strategy"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Icon (Emoji or Text)
                  </label>
                  <Input
                    value={featureFormData.icon}
                    onChange={(e) =>
                      setFeatureFormData({ ...featureFormData, icon: e.target.value })
                    }
                    className="bg-dark-bg border-red/30 text-white"
                    placeholder="e.g., 💼 or 🚀 or any emoji"
                    required
                  />
                  <p className="text-xs text-gray-400 mt-1">
                    Use emojis like 💼, 💻, 📊 or any text/symbol
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Description
                  </label>
                  <Textarea
                    value={featureFormData.description}
                    onChange={(e) =>
                      setFeatureFormData({ ...featureFormData, description: e.target.value })
                    }
                    className="bg-dark-bg border-red/30 text-white min-h-[100px]"
                    placeholder="Describe what this feature/service includes..."
                    required
                  />
                </div>

                <div className="flex items-center gap-3 pt-4">
                  <Button
                    type="submit"
                    className="bg-red hover:bg-red/80 flex-1"
                  >
                    {editingFeature ? "Update Feature" : "Add Feature"}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowFeatureForm(false);
                      setEditingFeature(null);
                    }}
                    className="border-red/30 text-red hover:bg-red/10"
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Resume Form Modal */}
      {showResumeForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-2 sm:p-4">
          <div className="bg-dark-lighter border border-red/20 rounded-xl max-w-2xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
            <div className="p-4 sm:p-6">
              <div className="flex items-center justify-between mb-4 sm:mb-6">
                <h3 className="text-lg sm:text-xl font-bold text-white">
                  {editingResumeItem
                    ? `Edit ${resumeFormType === "education" ? "Education" : "Experience"}`
                    : `Add ${resumeFormType === "education" ? "Education" : "Experience"}`}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowResumeForm(false);
                    setEditingResumeItem(null);
                  }}
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </Button>
              </div>

              <form onSubmit={handleResumeSubmit} className="space-y-4">
                {resumeFormType === "education" ? (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Degree *
                      </label>
                      <Input
                        value={resumeFormData.degree}
                        onChange={(e) =>
                          setResumeFormData({
                            ...resumeFormData,
                            degree: e.target.value,
                          })
                        }
                        className="bg-dark-bg border-red/30 text-white"
                        placeholder="e.g., BSC in Computer Science"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Institution *
                      </label>
                      <Input
                        value={resumeFormData.institution}
                        onChange={(e) =>
                          setResumeFormData({
                            ...resumeFormData,
                            institution: e.target.value,
                          })
                        }
                        className="bg-dark-bg border-red/30 text-white"
                        placeholder="e.g., Harvard University"
                        required
                      />
                    </div>
                  </>
                ) : (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Position *
                      </label>
                      <Input
                        value={resumeFormData.position}
                        onChange={(e) =>
                          setResumeFormData({
                            ...resumeFormData,
                            position: e.target.value,
                          })
                        }
                        className="bg-dark-bg border-red/30 text-white"
                        placeholder="e.g., Sr. Software Engineer"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Company *
                      </label>
                      <Input
                        value={resumeFormData.company}
                        onChange={(e) =>
                          setResumeFormData({
                            ...resumeFormData,
                            company: e.target.value,
                          })
                        }
                        className="bg-dark-bg border-red/30 text-white"
                        placeholder="e.g., Google Inc."
                        required
                      />
                    </div>
                  </>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Period *
                  </label>
                  <Input
                    value={resumeFormData.period}
                    onChange={(e) =>
                      setResumeFormData({
                        ...resumeFormData,
                        period: e.target.value,
                      })
                    }
                    className="bg-dark-bg border-red/30 text-white"
                    placeholder="e.g., 2021-Present or 2015-2019"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Description *
                  </label>
                  <Textarea
                    value={resumeFormData.description}
                    onChange={(e) =>
                      setResumeFormData({
                        ...resumeFormData,
                        description: e.target.value,
                      })
                    }
                    className="bg-dark-bg border-red/30 text-white min-h-[100px]"
                    placeholder="Describe your role, achievements, or what you learned..."
                    required
                  />
                </div>

                <div className="flex items-center gap-3 pt-4">
                  <Button
                    type="submit"
                    className="bg-red hover:bg-red/80"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {editingResumeItem ? "Update" : "Add"} {resumeFormType === "education" ? "Education" : "Experience"}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowResumeForm(false);
                      setEditingResumeItem(null);
                    }}
                    className="border-red/30 text-red hover:bg-red/10"
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
};

export default AdminContent;
