# Kenenis Dev Portfolio - Personal Portfolio Website

## Project Overview

A modern, responsive personal portfolio website built with React, TypeScript, and Tailwind CSS. Features a clean design with dark/light theme support, admin panel for content management, and real-time updates.

## Getting Started

### Prerequisites

- Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

### Installation

Follow these steps to set up the project locally:

```sh
# Step 1: Clone the repository
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies
npm install

# Step 4: Start the development server
npm run dev
```

### Development

You can edit this project using any preferred IDE or code editor. The development server supports hot reloading for instant preview of changes.

## Technologies Used

This project is built with modern web technologies:

- **Frontend**: React 18 with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **Build Tool**: Vite
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **State Management**: React Query (TanStack Query)
- **Routing**: React Router DOM
- **Icons**: Lucide React
- **Theme**: Next Themes (Dark/Light mode)

## Features

- 🎨 Modern, responsive design
- 🌙 Dark/Light theme support
- 📱 Mobile-first approach
- 🔐 Admin panel with authentication
- 📊 Real-time content management
- 💼 Portfolio showcase
- 📝 Contact form with database storage
- 🎯 SEO optimized
- ⚡ Fast performance with Vite

## Deployment

You can deploy this project to any static hosting service like:

- Vercel
- Netlify
- GitHub Pages
- Firebase Hosting

### Build for Production

```sh
npm run build
```

The built files will be in the `dist` directory.
