import React, { useState, useRef, useEffect } from "react";
import {
  Bell,
  MessageSquare,
  Star,
  Clock,
  Check,
  CheckCheck,
  X,
  Mail,
  User,
} from "lucide-react";
import { useNotifications, NotificationItem } from "@/hooks/useNotifications";
// Simple date formatting function
const formatDistanceToNow = (date: Date): string => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return "just now";
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
  return `${Math.floor(diffInSeconds / 604800)}w ago`;
};
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";

interface NotificationDropdownProps {
  onNavigateToMessages?: () => void;
  onNavigateToTestimonials?: () => void;
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({
  onNavigateToMessages,
  onNavigateToTestimonials,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotifications();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleNotificationClick = async (notification: NotificationItem) => {
    await markAsRead(notification.id);
    
    // Navigate to appropriate section
    if (notification.type === "message" && onNavigateToMessages) {
      onNavigateToMessages();
    } else if (notification.type === "testimonial" && onNavigateToTestimonials) {
      onNavigateToTestimonials();
    }
    
    setIsOpen(false);
  };

  const handleMarkAllRead = async () => {
    await markAllAsRead();
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "message":
        return <MessageSquare className="w-4 h-4 text-blue-400" />;
      case "testimonial":
        return <Star className="w-4 h-4 text-yellow-400" />;
      default:
        return <Bell className="w-4 h-4 text-gray-400" />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch {
      return "Unknown time";
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Bell Icon Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-400 hover:text-white transition-colors duration-200 rounded-lg hover:bg-gray-800/50"
        aria-label={`Notifications (${unreadCount} unread)`}
      >
        <Bell className="w-5 h-5" />
        {unreadCount > 0 && (
          <div className="absolute -top-1 -right-1 min-w-[18px] h-[18px] bg-red rounded-full flex items-center justify-center text-white text-xs font-medium animate-pulse">
            {unreadCount > 99 ? "99+" : unreadCount}
          </div>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-96 bg-dark-bg border border-gray-700/50 rounded-xl shadow-2xl z-50 overflow-hidden">
          {/* Header */}
          <div className="p-4 border-b border-gray-700/50 bg-gray-800/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Bell className="w-5 h-5 text-red" />
                <h3 className="text-white font-semibold">Notifications</h3>
                {unreadCount > 0 && (
                  <span className="bg-red/20 text-red text-xs px-2 py-1 rounded-full">
                    {unreadCount} new
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2">
                {unreadCount > 0 && (
                  <Button
                    onClick={handleMarkAllRead}
                    variant="ghost"
                    size="sm"
                    className="text-xs text-gray-400 hover:text-white"
                  >
                    <CheckCheck className="w-3 h-3 mr-1" />
                    Mark all read
                  </Button>
                )}
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Notifications List */}
          <ScrollArea className="max-h-96">
            {notifications.length === 0 ? (
              <div className="p-8 text-center">
                <Bell className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                <p className="text-gray-400 text-sm">No notifications yet</p>
                <p className="text-gray-500 text-xs mt-1">
                  New messages and testimonials will appear here
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-700/30">
                {notifications.slice(0, 10).map((notification) => (
                  <div
                    key={notification.id}
                    onClick={() => handleNotificationClick(notification)}
                    className={`p-4 hover:bg-gray-800/30 cursor-pointer transition-colors duration-200 ${
                      !notification.isRead ? "bg-red/5 border-l-2 border-l-red" : ""
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      {/* Icon */}
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <h4 className={`text-sm font-medium truncate ${
                            !notification.isRead ? "text-white" : "text-gray-300"
                          }`}>
                            {notification.title}
                          </h4>
                          {!notification.isRead && (
                            <div className="w-2 h-2 bg-red rounded-full flex-shrink-0 mt-2" />
                          )}
                        </div>
                        
                        <p className="text-gray-400 text-xs mt-1 line-clamp-2">
                          {notification.content}
                        </p>
                        
                        <div className="flex items-center gap-2 mt-2">
                          <User className="w-3 h-3 text-gray-500" />
                          <span className="text-gray-500 text-xs">
                            {notification.sender}
                          </span>
                          <span className="text-gray-600 text-xs">•</span>
                          <Clock className="w-3 h-3 text-gray-500" />
                          <span className="text-gray-500 text-xs">
                            {formatTimestamp(notification.timestamp)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className="p-3 border-t border-gray-700/50 bg-gray-800/20">
              <div className="flex gap-2">
                <Button
                  onClick={() => {
                    onNavigateToMessages?.();
                    setIsOpen(false);
                  }}
                  variant="ghost"
                  size="sm"
                  className="flex-1 text-xs text-gray-400 hover:text-white"
                >
                  <Mail className="w-3 h-3 mr-1" />
                  View Messages
                </Button>
                <Button
                  onClick={() => {
                    onNavigateToTestimonials?.();
                    setIsOpen(false);
                  }}
                  variant="ghost"
                  size="sm"
                  className="flex-1 text-xs text-gray-400 hover:text-white"
                >
                  <Star className="w-3 h-3 mr-1" />
                  View Testimonials
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default NotificationDropdown;
