<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e293b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f172a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="k-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff014f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f9004d;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background rounded rectangle -->
  <rect x="0" y="0" width="512" height="512" rx="128" ry="128" fill="url(#bg-gradient)"/>
  
  <!-- Modern K letter (scaled up) -->
  <g transform="translate(128, 100)">
    <!-- Left vertical line of K -->
    <rect x="0" y="0" width="54" height="312" fill="url(#k-gradient)" rx="27"/>
    
    <!-- Upper diagonal of K -->
    <path d="M70 0 L256 156 L218 194 L70 86 Z" fill="url(#k-gradient)"/>
    
    <!-- Lower diagonal of K -->
    <path d="M70 226 L218 118 L256 156 L70 312 Z" fill="url(#k-gradient)"/>
  </g>
  
  <!-- Subtle glow effect -->
  <rect x="8" y="8" width="496" height="496" rx="120" ry="120" fill="none" stroke="url(#k-gradient)" stroke-width="4" opacity="0.3"/>
</svg>
