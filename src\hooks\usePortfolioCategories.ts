import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "../services/supabase/client";
import { toast } from "sonner";

export interface PortfolioCategory {
  id: string;
  name: string;
  description?: string;
  color: string;
  display_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export const usePortfolioCategories = () => {
  const queryClient = useQueryClient();

  // Fetch categories
  const {
    data: categories,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["portfolio-categories"],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("portfolio_categories")
          .select("*")
          .eq("is_active", true)
          .order("display_order", { ascending: true });

        if (error) {
          console.error("Error fetching portfolio categories:", error);
          throw error;
        }

        return data as PortfolioCategory[];
      } catch (error) {
        console.error("Database unavailable for fetching categories:", error);
        // Return default categories as fallback
        return [
          { id: "all", name: "All", description: "Show all portfolio items", color: "#6366f1", display_order: 0, is_active: true, created_at: "", updated_at: "" },
          { id: "web-dev", name: "Web Development", description: "Full-stack web applications", color: "#ef4444", display_order: 1, is_active: true, created_at: "", updated_at: "" },
          { id: "mobile", name: "Mobile App", description: "Mobile applications", color: "#10b981", display_order: 2, is_active: true, created_at: "", updated_at: "" },
          { id: "design", name: "Web Design", description: "UI/UX design", color: "#f59e0b", display_order: 3, is_active: true, created_at: "", updated_at: "" },
        ] as PortfolioCategory[];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Create category
  const createCategoryMutation = useMutation({
    mutationFn: async (category: Omit<PortfolioCategory, "id" | "created_at" | "updated_at">) => {
      try {
        const { data, error } = await supabase
          .from("portfolio_categories")
          .insert(category)
          .select()
          .single();

        if (error) {
          console.error("Error creating portfolio category:", error);
          throw error;
        }

        return data as PortfolioCategory;
      } catch (error) {
        console.error("Database unavailable for creating category:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["portfolio-categories"] });
      toast.success("Category created successfully!");
    },
    onError: (error) => {
      console.error("Error creating category:", error);
      toast.error("Failed to create category");
    },
  });

  // Update category
  const updateCategoryMutation = useMutation({
    mutationFn: async ({ id, ...updates }: Partial<PortfolioCategory> & { id: string }) => {
      const { data, error } = await supabase
        .from("portfolio_categories")
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .select()
        .single();

      if (error) {
        console.error("Error updating portfolio category:", error);
        throw error;
      }

      return data as PortfolioCategory;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["portfolio-categories"] });
      toast.success("Category updated successfully!");
    },
    onError: (error) => {
      console.error("Error updating category:", error);
      toast.error("Failed to update category");
    },
  });

  // Delete category
  const deleteCategoryMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from("portfolio_categories")
        .delete()
        .eq("id", id);

      if (error) {
        console.error("Error deleting portfolio category:", error);
        throw error;
      }
      return id;
    },
    onSuccess: (id) => {
      queryClient.invalidateQueries({ queryKey: ["portfolio-categories"] });
      toast.success("Category deleted successfully!");
    },
    onError: (error) => {
      console.error("Error deleting category:", error);
      toast.error("Failed to delete category");
    },
  });

  // Get category names for dropdown
  const getCategoryNames = () => {
    if (!categories) return [];
    return categories.filter(cat => cat.name !== "All").map(cat => cat.name);
  };

  return {
    categories: categories || [],
    isLoading,
    error,
    getCategoryNames,
    createCategory: createCategoryMutation.mutate,
    updateCategory: updateCategoryMutation.mutate,
    deleteCategory: deleteCategoryMutation.mutate,
    isCreating: createCategoryMutation.isPending,
    isUpdating: updateCategoryMutation.isPending,
    isDeleting: deleteCategoryMutation.isPending,
  };
};
