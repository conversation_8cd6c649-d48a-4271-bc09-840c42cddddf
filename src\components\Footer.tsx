import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import ThemeToggle from "./ThemeToggle";

const Footer = () => {
  const navigate = useNavigate();
  const [isHolding, setIsHolding] = useState(false);
  const [holdProgress, setHoldProgress] = useState(0);
  const [countdown, setCountdown] = useState(30);
  const holdTimerRef = useRef<NodeJS.Timeout | null>(null);
  const progressTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Clean up timers on unmount
  useEffect(() => {
    return () => {
      if (holdTimerRef.current) {
        clearTimeout(holdTimerRef.current);
      }
      if (progressTimerRef.current) {
        clearInterval(progressTimerRef.current);
      }
    };
  }, []);

  const handleMouseDown = () => {
    const HOLD_TIME = 5; // 5 seconds - perfect timing

    setIsHolding(true);
    setHoldProgress(0);
    setCountdown(HOLD_TIME);

    // No toast notification to keep it hidden

    // Start progress animation and countdown
    progressTimerRef.current = setInterval(() => {
      setHoldProgress(prev => {
        const newProgress = prev + (100 / HOLD_TIME / 10); // HOLD_TIME seconds = HOLD_TIME*1000ms / 100ms intervals
        if (newProgress >= 100) {
          // Clear timers first
          if (holdTimerRef.current) {
            clearTimeout(holdTimerRef.current);
          }
          if (progressTimerRef.current) {
            clearInterval(progressTimerRef.current);
          }

          // Redirect immediately when progress reaches 100% (no toast to keep it hidden)

          setIsHolding(false);
          setHoldProgress(0);
          setCountdown(HOLD_TIME);

          navigate("/admin");
          return 100;
        }
        return newProgress;
      });

      setCountdown(prev => {
        const newCountdown = prev - 0.1;
        return newCountdown <= 0 ? 0 : newCountdown;
      });
    }, 100);

    // Backup timer (in case interval fails)
    holdTimerRef.current = setTimeout(() => {
      navigate("/admin");
      setIsHolding(false);
      setHoldProgress(0);
      setCountdown(HOLD_TIME);
    }, HOLD_TIME * 1000);
  };

  const handleMouseUp = () => {
    // Clear timers and reset state
    if (holdTimerRef.current) {
      clearTimeout(holdTimerRef.current);
    }
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
    }
    setIsHolding(false);
    setHoldProgress(0);
    setCountdown(5);
  };

  const handleMouseLeave = () => {
    // Same as mouse up - cancel the hold
    handleMouseUp();
  };

  return (
    <footer className="bg-dark-lighter py-8 lg:py-12 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-t from-red/5 via-transparent to-transparent pointer-events-none"></div>

      <div className="container mx-auto px-4 lg:px-8 relative z-10">
        {/* Main Footer Content */}
        <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
          {/* Copyright Section */}
          <div className="text-center lg:text-left">
            <div className="footer-text text-slate-600 dark:text-gray-300 text-sm md:text-base lg:text-lg">
              © {new Date().getFullYear()} - All rights reserved by{" "}
              <span
                className="footer-name text-red font-semibold cursor-pointer select-none relative"
                onMouseDown={handleMouseDown}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseLeave}
                onTouchStart={handleMouseDown}
                onTouchEnd={handleMouseUp}
              >
                Kenenis Dev
                {/* Nearly invisible progress indicator */}
                {isHolding && (
                  <div className="absolute -bottom-1 left-0 w-full h-px bg-transparent overflow-hidden">
                    <div
                      className="h-full bg-slate-600/10 dark:bg-gray-300/5 transition-all duration-100 ease-linear"
                      style={{ width: `${holdProgress}%` }}
                    />
                  </div>
                )}
              </span>
            </div>

            {/* Subtitle */}
            <div className="footer-subtitle mt-2">
              <p className="text-slate-500 dark:text-gray-400 text-xs md:text-sm">
                Crafted with passion and precision
              </p>
            </div>
          </div>

          {/* Theme Toggle Section */}
          <div className="theme-toggle-section flex items-center justify-center lg:justify-end">
            <div className="theme-toggle-wrapper p-3 rounded-xl bg-gradient-to-br from-white/80 to-gray-100/60 dark:from-gray-800/30 dark:to-gray-900/30 backdrop-blur-sm border border-gray-300/40 dark:border-gray-700/30 hover:border-red/30 dark:hover:border-gray-600/50 transition-all duration-300 shadow-lg hover:shadow-xl">
              <ThemeToggle />
            </div>
          </div>
        </div>


      </div>
    </footer>
  );
};

export default Footer;
