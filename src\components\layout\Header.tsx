import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Menu,
  X,
  Home,
  User,
  FileText,
  Briefcase,
  MessageSquare,
  Star,
  Github,
  Linkedin,
  Twitter,
  Mail,
  Phone,
  MapPin,

  Instagram,
  Facebook,
  Download,
  Zap,
} from "lucide-react";
import {
  Drawer,
  DrawerContent,
  DrawerOverlay,
  DrawerTrigger,
  DrawerTitle,
  DrawerDescription,
} from "../ui/drawer";

import { Avatar, AvatarImage, AvatarFallback } from "../ui/avatar";
import { useHeroContent } from "../../hooks/useHeroContent";
import { useCVDownload } from "../../hooks/useCVDownload";
import { usePortfolioContent } from "../../hooks/usePortfolioContent";

const Header = () => {
  const navigate = useNavigate();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState("home");
  const [scrolled, setScrolled] = useState(false);
  const [typingIndex, setTypingIndex] = useState(0);
  const { heroContent } = useHeroContent();
  const { downloadCV, isDownloadEnabled } = useCVDownload();
  const { content } = usePortfolioContent();

  // Contact info from database with fallback
  const [contactData, setContactData] = useState({
    phone: "+****************",
    email: "<EMAIL>",
    address: "Remote Developer",
    availability: "Available for work",
    socialLinks: {
      linkedin: "https://linkedin.com/in/kenenisdev",
      github: "https://github.com/kenenisdev",
      twitter: "https://twitter.com/kenenisdev",
      instagram: "https://instagram.com/kenenisdev",
      facebook: "https://facebook.com/kenenisdev",
    },
    socialVisibility: {
      linkedin: true,
      github: true,
      twitter: true,
      instagram: true,
      facebook: true,
    },
  });

  // Load contact info from database
  useEffect(() => {
    if (content && content.length > 0) {
      const contactInfo = content.find(item => item.section === "contact")?.content;
      if (contactInfo) {
        setContactData((prev) => ({ ...prev, ...contactInfo }));
      }
    }
  }, [content]);

  const navItems = [
    { id: "home", label: "Home", icon: Home },
    { id: "about", label: "About", icon: User },
    { id: "resume", label: "Resume", icon: FileText },
    { id: "skills", label: "Skills", icon: Zap },
    { id: "portfolio", label: "Portfolio", icon: Briefcase },
    { id: "blog", label: "Blog", icon: FileText, isRoute: true, route: "/blog" },
    { id: "testimonials", label: "Testimonials", icon: Star },
    { id: "contact", label: "Contact", icon: MessageSquare },
  ];

  const socialLinks = [
    {
      icon: Linkedin,
      href: contactData.socialLinks?.linkedin || "#",
      label: "LinkedIn",
      visible: contactData.socialVisibility?.linkedin
    },
    {
      icon: Github,
      href: contactData.socialLinks?.github || "#",
      label: "GitHub",
      visible: contactData.socialVisibility?.github
    },
    {
      icon: Twitter,
      href: contactData.socialLinks?.twitter || "#",
      label: "Twitter",
      visible: contactData.socialVisibility?.twitter
    },
    {
      icon: Instagram,
      href: contactData.socialLinks?.instagram || "#",
      label: "Instagram",
      visible: contactData.socialVisibility?.instagram
    },
    {
      icon: Facebook,
      href: contactData.socialLinks?.facebook || "#",
      label: "Facebook",
      visible: contactData.socialVisibility?.facebook
    },
  ].filter(link => link.href !== "#" && link.visible); // Only show links that are configured and visible

  const contactInfo = [
    {
      icon: Phone,
      label: "Phone",
      value: contactData.phone,
      href: `tel:${contactData.phone.replace(/\s/g, '')}`,
    },
    {
      icon: Mail,
      label: "Email",
      value: contactData.email,
      href: `mailto:${contactData.email}`,
    },
    {
      icon: MapPin,
      label: "Location",
      value: contactData.address,
      href: "#"
    },
  ];

  const profession = heroContent.profession;

  useEffect(() => {
    // Handle typing animation for profession text in mobile menu
    if (mobileMenuOpen) {
      const typingInterval = setInterval(() => {
        setTypingIndex((prev) => {
          if (prev >= profession.length) {
            // Reset after showing full text for 1.5s
            setTimeout(() => setTypingIndex(0), 1500);
            return prev;
          }
          return prev + 1;
        });
      }, 150);

      return () => clearInterval(typingInterval);
    } else {
      // Reset typing index when menu is closed
      setTypingIndex(0);
    }
  }, [mobileMenuOpen]);

  useEffect(() => {
    const handleScroll = () => {
      // Update header background when scrolled
      setScrolled(window.scrollY > 50);

      // Update active section based on scroll position
      const sections = navItems.map((item) => document.getElementById(item.id));
      let currentActive = "home";

      sections.forEach((section) => {
        if (section) {
          const sectionTop = section.offsetTop;
          const sectionHeight = section.offsetHeight;
          if (
            window.scrollY >= sectionTop - 100 &&
            window.scrollY < sectionTop + sectionHeight - 100
          ) {
            currentActive = section.id;
          }
        }
      });

      setActiveSection(currentActive);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.classList.add("mobile-menu-open");
    } else {
      document.body.classList.remove("mobile-menu-open");
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove("mobile-menu-open");
    };
  }, [mobileMenuOpen]);

  // Handle smooth scroll to section (shared function)
  const scrollToSection = (id: string, isMobile: boolean = false) => {
    const element = document.getElementById(id);
    if (element) {
      // Calculate offset for fixed header - different for mobile and desktop
      const headerOffset = isMobile ? 80 : 100; // Slightly less offset for mobile
      const elementPosition = element.offsetTop;
      const offsetPosition = elementPosition - headerOffset;

      window.scrollTo({
        top: Math.max(0, offsetPosition), // Ensure we don't scroll to negative position
        behavior: "smooth",
      });
    }
  };

  // Handle desktop navigation click
  const handleDesktopNavClick = (id: string, event: React.MouseEvent) => {
    event.preventDefault(); // Prevent default anchor behavior

    // Find the nav item to check if it's a route
    const navItem = navItems.find(item => item.id === id);

    if (navItem && navItem.isRoute && navItem.route) {
      // Navigate to the route
      navigate(navItem.route);
    } else {
      // Scroll to section
      scrollToSection(id, false); // false = not mobile
    }
  };

  // Handle mobile menu link click - close drawer and scroll to section
  const handleMobileNavClick = (id: string, event: React.MouseEvent) => {
    event.preventDefault(); // Prevent default anchor behavior
    event.stopPropagation(); // Stop event bubbling

    // Add click feedback
    const button = event.currentTarget;
    button.classList.add("nav-item-clicked");
    setTimeout(() => button.classList.remove("nav-item-clicked"), 150);

    // Close mobile menu first
    setMobileMenuOpen(false);

    // Find the nav item to check if it's a route
    const navItem = navItems.find(item => item.id === id);

    if (navItem && navItem.isRoute && navItem.route) {
      // Navigate to the route
      navigate(navItem.route);
    } else {
      // Longer delay to allow menu to fully close before scrolling
      setTimeout(() => {
        scrollToSection(id, true); // true = mobile
      }, 300);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Escape" && mobileMenuOpen) {
      setMobileMenuOpen(false);
    }
  };

  return (
    <header
      className={`fixed top-0 right-0 left-0 lg:left-[380px] z-[100] transition-all duration-500 ${
        scrolled
          ? "stunning-header-scrolled backdrop-blur-xl shadow-2xl"
          : "stunning-header-transparent"
      } overflow-hidden`}
      style={{ position: "fixed" }}
    >
      {/* Stunning Background Effects */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Primary Gradient Background */}
        <div
          className={`absolute inset-0 transition-opacity duration-500 ${
            scrolled ? "opacity-100" : "opacity-0"
          } bg-gradient-to-r from-dark-bg/95 via-dark-lighter/90 to-dark-bg/95`}
        ></div>

        {/* Animated Gradient Overlay */}
        <div
          className={`absolute inset-0 transition-opacity duration-500 ${
            scrolled ? "opacity-100" : "opacity-30"
          } bg-gradient-to-r from-red/10 via-transparent to-red/10 animate-pulse`}
        ></div>

        {/* Subtle Pattern Overlay */}
        <div
          className={`absolute inset-0 transition-opacity duration-500 ${
            scrolled ? "opacity-20" : "opacity-10"
          } header-pattern`}
        ></div>

        {/* Glow Effect */}
        <div
          className={`absolute inset-0 transition-opacity duration-500 ${
            scrolled ? "opacity-100" : "opacity-0"
          } bg-gradient-to-b from-red/5 via-transparent to-transparent`}
        ></div>

        {/* Floating Particles Effect */}
        <div
          className={`absolute inset-0 transition-opacity duration-700 ${
            scrolled ? "opacity-100" : "opacity-60"
          } header-particles`}
        >
          <div className="header-particle header-particle-1"></div>
          <div className="header-particle header-particle-2"></div>
          <div className="header-particle header-particle-3"></div>
        </div>
      </div>

      {/* Content Wrapper with proper z-index */}
      <div className="container mx-auto px-2 lg:px-4 py-2 sm:py-3 lg:py-[34px] relative z-10">
        <div className="flex justify-between items-center lg:justify-center lg:relative">
          {/* Mobile Section Title - Only show on smaller screens */}
          <div className="lg:hidden">
            <h1 className="enhanced-header-title-mobile">
              {navItems.find((item) => item.id === activeSection)?.label ||
                "Home"}
              <div className="header-title-underline-mobile"></div>
            </h1>
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <Drawer
              open={mobileMenuOpen}
              onOpenChange={setMobileMenuOpen}
              direction="right"
            >
              <DrawerTrigger asChild>
                <button
                  className="relative w-12 h-12 flex flex-col justify-center items-center text-red hover:text-white focus:outline-none transition-all duration-400 group stunning-hamburger-btn hover:scale-105 active:scale-95"
                  aria-label={
                    mobileMenuOpen
                      ? "Close navigation menu"
                      : "Open navigation menu"
                  }
                  aria-expanded={mobileMenuOpen}
                  aria-controls="mobile-navigation-menu"
                >
                  {/* Enhanced Hamburger Lines */}
                  <div className="relative w-7 h-5 flex flex-col justify-between">
                    <span
                      className={`block w-full h-0.5 rounded-full transition-all duration-400 ease-in-out transform origin-center ${
                        mobileMenuOpen
                          ? "rotate-45 translate-y-2"
                          : "group-hover:bg-white group-hover:scale-110"
                      }`}
                      style={{
                        backgroundColor: "#ff6b9d",
                      }}
                    ></span>
                    <span
                      className={`block w-full h-0.5 rounded-full transition-all duration-300 ease-in-out transform origin-center ${
                        mobileMenuOpen
                          ? "opacity-0 scale-0 rotate-180"
                          : "group-hover:bg-white group-hover:scale-110"
                      }`}
                      style={{
                        backgroundColor: "#ff6b9d",
                      }}
                    ></span>
                    <span
                      className={`block w-full h-0.5 rounded-full transition-all duration-400 ease-in-out transform origin-center ${
                        mobileMenuOpen
                          ? "-rotate-45 -translate-y-2"
                          : "group-hover:bg-white group-hover:scale-110"
                      }`}
                      style={{
                        backgroundColor: "#ff6b9d",
                      }}
                    ></span>
                  </div>
                </button>
              </DrawerTrigger>

              <DrawerContent
                className="fixed inset-y-0 left-0 z-50 mobile-menu-full-height w-80 max-w-[80vw] border-r border-gray-800/50 bg-gradient-to-br from-dark-bg/95 to-dark-lighter/95 backdrop-blur-xl shadow-2xl transition-all duration-500 ease-out mobile-menu-content overflow-hidden"
                style={{
                  transform: mobileMenuOpen
                    ? "translateX(0)"
                    : "translateX(-100%)",
                }}
                onKeyDown={handleKeyDown}
              >
                {/* Accessibility components - visually hidden */}
                <DrawerTitle className="sr-only">Navigation Menu</DrawerTitle>
                <DrawerDescription className="sr-only">
                  Main navigation menu with links to different sections of the
                  website
                </DrawerDescription>

                <div className="flex flex-col mobile-menu-full-height relative overflow-hidden">
                  {/* Gradient Background Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-b from-red/5 via-transparent to-red/10 pointer-events-none"></div>

                  {/* Header with Close Button */}
                  <div className="flex justify-end items-start p-4 pt-6 pr-6 relative z-10 flex-shrink-0">
                    <button
                      onClick={() => setMobileMenuOpen(false)}
                      className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-800/30 text-gray-300 hover:text-red hover:bg-red/10 focus:outline-none transition-all duration-300 backdrop-blur-sm hover:scale-110"
                      aria-label="Close navigation menu"
                    >
                      <X size={16} />
                    </button>
                  </div>

                  {/* Scrollable Content */}
                  <div className="flex-1 overflow-y-auto relative z-10 mobile-menu-scrollable">
                    {/* Clean Profile Section */}
                    <div className="px-6 pb-6 pt-4">
                      {/* Horizontal Layout - No Card */}
                      <div className="flex items-start gap-3">
                        {/* Small Profile Avatar */}
                        <div className="relative w-12 h-12 flex-shrink-0">
                          {/* Avatar Glow Ring */}
                          <div className="absolute inset-0 rounded-full bg-gradient-to-r from-red/20 via-red/10 to-red/20 blur-sm animate-pulse"></div>

                          {/* Avatar Image */}
                          <Avatar className="relative w-full h-full ring-1 ring-red/30 shadow-lg hover:scale-105 transition-all duration-300">
                            <AvatarImage
                              src={heroContent.profileImage}
                              alt={heroContent.name}
                              className="object-cover"
                            />
                            <AvatarFallback className="bg-red/20 text-red font-bold text-sm">
                              {heroContent.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>

                          {/* Status Dot */}
                          <div className="absolute -top-0.5 -right-0.5 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-800 animate-pulse"></div>
                        </div>

                        {/* Profile Info */}
                        <div className="flex-1 min-w-0 pt-1">
                          {/* Name - Better Aligned */}
                          <h3 className="text-base font-semibold text-white mb-0.5 truncate leading-tight">
                            {heroContent.name}
                          </h3>

                          {/* Availability Status - Compact */}
                          <span className="text-green-400 text-xs font-medium truncate block">
                            {contactData.availability || "Available for work"}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Navigation Links */}
                    <nav
                      className="px-6 pb-6"
                      id="mobile-navigation-menu"
                      role="navigation"
                      aria-label="Main navigation"
                    >
                      <ul className="flex flex-col space-y-2" role="list">
                        {navItems.map((item, index) => {
                          const IconComponent = item.icon;
                          return (
                            <li key={item.id} className="mobile-nav-item">
                              <button
                                onClick={(e) =>
                                  handleMobileNavClick(item.id, e)
                                }
                                className={`w-full flex items-center gap-4 p-4 rounded-xl transition-all duration-300 group transform hover:scale-105 ${
                                  activeSection === item.id
                                    ? "bg-red/20 text-red border border-red/30 shadow-lg shadow-red/10 nav-item-active"
                                    : "text-gray-300 hover:text-white hover:bg-gray-800/50 border border-transparent hover:border-gray-700/50"
                                }`}
                                aria-current={
                                  activeSection === item.id ? "page" : undefined
                                }
                                aria-label={`Navigate to ${item.label} section`}
                              >
                                <div
                                  className={`w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-300 ${
                                    activeSection === item.id
                                      ? "bg-red/30 text-red"
                                      : "bg-gray-800/50 text-gray-400 group-hover:bg-red/20 group-hover:text-red"
                                  }`}
                                >
                                  <IconComponent size={18} />
                                </div>
                                <span className="font-medium text-base">
                                  {item.label}
                                </span>
                                <div
                                  className={`ml-auto w-2 h-2 rounded-full transition-all duration-300 ${
                                    activeSection === item.id
                                      ? "bg-red"
                                      : "bg-transparent"
                                  }`}
                                ></div>
                              </button>
                            </li>
                          );
                        })}
                      </ul>
                    </nav>

                    {/* Contact Information */}
                    <div className="px-6 pb-4">
                      <div className="border-t border-gray-800/50 pt-6">
                        <h4 className="text-gray-400 text-sm mb-4 text-center font-medium">
                          Contact Information
                        </h4>
                        <div className="space-y-3">
                          {contactInfo.map((contact, index) => {
                            const IconComponent = contact.icon;
                            return (
                              <a
                                key={index}
                                href={contact.href}
                                className="flex items-center gap-3 p-3 rounded-lg bg-gray-800/30 hover:bg-gray-800/50 transition-all duration-300 group"
                              >
                                <div className="w-8 h-8 rounded-lg bg-red/20 flex items-center justify-center text-red group-hover:bg-red/30 transition-colors duration-300">
                                  <IconComponent size={14} />
                                </div>
                                <div className="flex-1">
                                  <p className="text-gray-400 text-xs">
                                    {contact.label}
                                  </p>
                                  <p className="text-gray-200 text-sm font-medium">
                                    {contact.value}
                                  </p>
                                </div>
                              </a>
                            );
                          })}
                        </div>
                      </div>
                    </div>

                    {/* Social Links */}
                    <div className="px-6 pb-6">
                      <div className="border-t border-gray-800/50 pt-6">
                        <p className="text-gray-400 text-sm mb-4 text-center">
                          Connect with me
                        </p>
                        <div className="flex justify-center gap-3">
                          {socialLinks.map((social, index) => {
                            const IconComponent = social.icon;
                            return (
                              <a
                                key={index}
                                href={social.href}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="social-link w-10 h-10 rounded-lg bg-gray-800/50 flex items-center justify-center text-gray-400 hover:text-red hover:bg-red/20"
                                aria-label={social.label}
                              >
                                <IconComponent size={16} />
                              </a>
                            );
                          })}
                        </div>
                      </div>
                    </div>

                    {/* Download CV Button */}
                    {isDownloadEnabled && (
                      <div className="px-6 pb-6">
                        <button
                          onClick={downloadCV}
                          className="w-full flex items-center justify-center gap-2 p-3 rounded-lg bg-red/20 text-red border border-red/30 hover:bg-red/30 transition-all duration-300 font-medium"
                        >
                          <Download size={16} />
                          <span>Download CV</span>
                        </button>
                      </div>
                    )}

                  </div>
                </div>
              </DrawerContent>
            </Drawer>
          </div>

          {/* Desktop Navigation - Perfectly centered with optimized spacing */}
          <nav className="hidden lg:block lg:absolute lg:left-1/2 lg:transform lg:-translate-x-1/2">
            <ul className="flex items-center space-x-4 xl:space-x-6">
              {navItems.map((item) => (
                <li key={item.id}>
                  <button
                    onClick={(e) => handleDesktopNavClick(item.id, e)}
                    className={`nav-link-dash relative px-3 xl:px-4 py-3 rounded-lg transition-all duration-300 hover:bg-red/10 font-medium text-sm xl:text-base whitespace-nowrap ${
                      activeSection === item.id
                        ? "text-red bg-red/10 shadow-lg shadow-red/20"
                        : "text-gray-300 hover:text-white"
                    }`}
                  >
                    {item.label}
                    {activeSection === item.id && (
                      <span
                        className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 h-0.5 bg-red rounded-full"
                        style={{
                          width: "0%",
                          animation: "nav-dash 0.5s forwards",
                        }}
                      ></span>
                    )}
                  </button>
                </li>
              ))}
            </ul>
          </nav>


        </div>
      </div>
    </header>
  );
};

export default Header;
