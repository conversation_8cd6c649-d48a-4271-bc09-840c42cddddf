import React, { useEffect, useRef, useState } from "react";
import { usePortfolioContent } from "@/hooks/usePortfolioContent";
import { supabase } from "@/integrations/supabase/client";

const Skills = () => {
  // Database integration
  const { content } = usePortfolioContent();

  // Animation refs for skill bars
  const skillRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Skills content state with defaults
  const [skillsContent, setSkillsContent] = useState({
    title: "Professional Skills",
    description: "My technical expertise and proficiency levels across various technologies and tools that drive innovation in web development.",
    highlightedWords: [],
    skills: [
      { name: "HTML, CSS & Bootstrap", percentage: 95 },
      { name: "JavaScript", percentage: 89 },
      { name: " TypeScript", percentage: 88 },
      { name: "React.js", percentage: 90 },
      { name: "Node.js & Express.js", percentage: 88 },
      { name: "MySQL & Rest API", percentage: 90 },
      { name: "Wordpress, Adobe photoshop & Figma", percentage: 78 },
      { name: "BashScript, Git & GitHub", percentage: 88 },
    ],
  });

  // Function to render text with highlighted words
  const renderTextWithHighlights = (text, highlightedWords = []) => {
    if (!highlightedWords || highlightedWords.length === 0) {
      return text;
    }

    let highlightedText = text;
    highlightedWords.forEach((word) => {
      const regex = new RegExp(`\\b(${word})\\b`, 'gi');
      highlightedText = highlightedText.replace(
        regex,
        '<span class="highlight-text">$1</span>'
      );
    });

    return <span dangerouslySetInnerHTML={{ __html: highlightedText }} />;
  };

  // Load skills from database
  useEffect(() => {
    if (content && content.length > 0) {
      const skillsData = content.find(item => item.section === "skills")?.content;
      if (skillsData) {
        setSkillsContent(prev => ({
          ...prev,
          title: skillsData.title || prev.title,
          description: skillsData.description || prev.description,
          highlightedWords: skillsData.highlightedWords || prev.highlightedWords,
          skills: skillsData.skills || prev.skills,
        }));
      }
    }
  }, [content]);

  // Real-time subscription for skills content updates
  useEffect(() => {
    const channel = supabase
      .channel("skills_content_changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "portfolio_content",
          filter: "section=eq.skills",
        },
        (payload) => {
          if (
            payload.eventType === "UPDATE" ||
            payload.eventType === "INSERT"
          ) {
            const newContent = payload.new as any;
            if (newContent.content) {
              setSkillsContent(prev => ({
                ...prev,
                title: newContent.content.title || prev.title,
                description: newContent.content.description || prev.description,
                highlightedWords: newContent.content.highlightedWords || prev.highlightedWords,
                skills: newContent.content.skills || prev.skills,
              }));
            }
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: "0px",
      threshold: 0.3,
    };

    const handleIntersect = (entries: IntersectionObserverEntry[]) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const skillItem = entry.target as HTMLElement;
          const progressBar = skillItem.querySelector(
            ".skill-bar-fill"
          ) as HTMLElement;
          const counter = skillItem.querySelector(
            ".counter-number"
          ) as HTMLElement;
          const skillBarGlow = skillItem.querySelector(
            ".skill-bar-glow"
          ) as HTMLElement;
          const percentage = parseInt(
            counter.getAttribute("data-percentage") || "0",
            10
          );

          // Add stunning animation classes
          skillItem.classList.add("skill-animating");
          progressBar.classList.add("skill-bar-animating");

          // Animate progress bar with stunning effect
          setTimeout(() => {
            progressBar.style.width = `${percentage}%`;
            progressBar.style.transition =
              "width 2.5s cubic-bezier(0.4, 0, 0.2, 1)";

            // Add glow effect during animation
            if (skillBarGlow) {
              skillBarGlow.style.opacity = "1";
              skillBarGlow.style.width = `${percentage}%`;
              skillBarGlow.style.transition =
                "width 2.5s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s ease";
            }
          }, 200);

          // Animate counter with clean integer values
          let count = 0;
          const duration = 20; // 2 seconds total animation
          const steps = percentage; // One step per percentage point
          const stepDuration = duration / steps; // Time per step

          const timer = setInterval(() => {
            count++;
            counter.textContent = `${count}%`;

            // Add pulsing effect during counting
            counter.style.transform = `scale(${
              1 + (count / percentage) * 0.1
            })`;
            counter.style.color = `hsl(${
              340 + (count / percentage) * 20
            }, 100%, ${60 + (count / percentage) * 10}%)`;

            if (count >= percentage) {
              clearInterval(timer);
              counter.textContent = `${percentage}%`;

              // Final effect when complete
              counter.style.transform = "scale(1.1)";
              counter.style.color = "#ff014f";

              setTimeout(() => {
                counter.style.transform = "scale(1)";
                counter.style.transition = "transform 0.3s ease";
              }, 200);

              // Hide glow after animation
              setTimeout(() => {
                if (skillBarGlow) {
                  skillBarGlow.style.opacity = "0";
                }
              }, 1000);
            }
          }, stepDuration);
        }
      });
    };

    const observer = new IntersectionObserver(handleIntersect, observerOptions);

    skillRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => {
      skillRefs.current.forEach((ref) => {
        if (ref) observer.unobserve(ref);
      });
    };
  }, []);

  return (
    <section
      id="skills"
      className="section bg-dark-darker relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="resume-bg-glow"></div>
      <div className="resume-particles"></div>
      <div className="resume-grid-pattern"></div>

      <div className="container mx-auto relative z-10">
        {/* Enhanced Section Header */}
        <div className="text-center mb-16">
          <div className="relative inline-block">
            <h2 className="enhanced-resume-title">{skillsContent.title}</h2>
            <div className="resume-title-glow"></div>
            <div className="resume-title-underline"></div>
          </div>

          <div className="resume-description-container">
            <p className="resume-description">
              {renderTextWithHighlights(skillsContent.description, skillsContent.highlightedWords)}
            </p>
          </div>
        </div>

        {/* Enhanced Skills Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-10 gap-y-8">
          {skillsContent.skills.map((skill, index) => (
            <div
              key={index}
              ref={(el) => (skillRefs.current[index] = el)}
              className="enhanced-skill-item"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="skill-header">
                <h4 className="skill-name">{skill.name}</h4>
                <span
                  className="skill-percentage counter-number"
                  data-percentage={skill.percentage}
                >
                  0%
                </span>
              </div>

              <div className="skill-bar-container">
                <div className="skill-bar-bg">
                  <div
                    className="skill-bar-fill progress-bar"
                    style={{ width: "0%" }}
                    data-percentage={skill.percentage}
                  ></div>
                  <div className="skill-bar-glow"></div>
                </div>
              </div>

              <div className="skill-particles"></div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Skills;
