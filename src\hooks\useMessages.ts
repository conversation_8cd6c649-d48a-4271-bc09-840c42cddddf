import React from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "../services/supabase/client";
import { toast } from "sonner";

export interface ContactMessage {
  id: string;
  name: string;
  email: string;
  phone: string | null;
  subject: string | null;
  message: string;
  status: string;
  priority: string;
  created_at: string;
  updated_at: string;
}

export const useMessages = () => {
  const queryClient = useQueryClient();

  // Fetch messages
  const {
    data: messages,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["contact-messages"],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("contact_messages")
          .select("*")
          .order("created_at", { ascending: false });

        if (error) {
          throw error;
        }

        return data as ContactMessage[];
      } catch (error) {
        return [];
      }
    },
    retry: false,
    staleTime: 5 * 60 * 1000,
  });

  // Set up real-time subscription
  React.useEffect(() => {
    const channel = supabase
      .channel("contact_messages_changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "contact_messages",
        },
        (payload) => {
          // Invalidate and refetch messages when changes occur
          queryClient.invalidateQueries({ queryKey: ["contact-messages"] });

          // Show notification for new messages
          if (payload.eventType === "INSERT") {
            const newMessage = payload.new as ContactMessage;
            toast.success(`New message from ${newMessage.name}!`, {
              description: newMessage.subject || "Contact form submission",
              duration: 5000,
            });
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);

  // Create message
  const createMessageMutation = useMutation({
    mutationFn: async (
      message: Omit<ContactMessage, "id" | "created_at" | "updated_at">
    ) => {
      const { error } = await supabase
        .from("contact_messages")
        .insert({
          ...message,
          status: message.status || "new",
          priority: message.priority || "medium",
        });

      if (error) throw error;
      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contact-messages"] });
      toast.success("Message sent successfully!");
    },
    onError: (error) => {
      if (!error.message?.includes("Failed to fetch")) {
        toast.error("Failed to send message");
      }
    },
  });

  // Update message status
  const updateMessageMutation = useMutation({
    mutationFn: async ({
      id,
      ...updates
    }: Partial<ContactMessage> & { id: string }) => {
      const { data, error } = await supabase
        .from("contact_messages")
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contact-messages"] });
      toast.success("Message updated successfully!");
    },
    onError: (error) => {
      console.error("Error updating message:", error);
      toast.error("Failed to update message");
    },
  });

  // Delete message
  const deleteMessageMutation = useMutation({
    mutationFn: async (id: string) => {
      const { data, error } = await supabase
        .from("contact_messages")
        .delete()
        .eq("id", id)
        .select();

      if (error) {
        throw error;
      }

      return data;
    },
    onMutate: async (messageId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["contact-messages"] });

      // Snapshot the previous value
      const previousMessages = queryClient.getQueryData(["contact-messages"]);

      // Optimistically update to remove the message
      queryClient.setQueryData(
        ["contact-messages"],
        (old: ContactMessage[] | undefined) => {
          if (!old) return [];
          return old.filter((message) => message.id !== messageId);
        }
      );

      // Return a context object with the snapshotted value
      return { previousMessages };
    },
    onError: (error, messageId, context) => {
      console.error("Error deleting message:", error);

      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousMessages) {
        queryClient.setQueryData(
          ["contact-messages"],
          context.previousMessages
        );
      }

      toast.error(`Failed to delete message: ${error.message}`);
    },
    onSettled: () => {
      // Always refetch after error or success to ensure we have the latest data
      queryClient.invalidateQueries({ queryKey: ["contact-messages"] });
    },
  });

  // Get message statistics
  const getMessageStats = () => {
    if (!messages) return { total: 0, new: 0, read: 0, replied: 0 };

    return {
      total: messages.length,
      new: messages.filter((m) => m.status === "new").length,
      read: messages.filter((m) => m.status === "read").length,
      replied: messages.filter((m) => m.status === "replied").length,
    };
  };

  return {
    messages,
    isLoading,
    error,
    createMessage: createMessageMutation.mutate,
    updateMessage: updateMessageMutation.mutate,
    deleteMessage: deleteMessageMutation.mutate,
    getMessageStats,
    isCreating: createMessageMutation.isPending,
    isUpdating: updateMessageMutation.isPending,
    isDeleting: deleteMessageMutation.isPending,
  };
};

export const useTestimonials = () => {
  const queryClient = useQueryClient();

  // Fetch testimonials
  const {
    data: testimonials,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["testimonials"],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("testimonials")
          .select("*")
          .order("created_at", { ascending: false });

        if (error) {
          throw error;
        }

        return data;
      } catch (error) {
        return [];
      }
    },
    retry: false,
    staleTime: 5 * 60 * 1000,
  });

  // Set up real-time subscription for testimonials
  React.useEffect(() => {
    const channel = supabase
      .channel("testimonials_changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "testimonials",
        },
        (payload) => {
          // Invalidate and refetch testimonials when changes occur
          queryClient.invalidateQueries({ queryKey: ["testimonials"] });

          // Show notification for new testimonials
          if (payload.eventType === "INSERT") {
            const newTestimonial = payload.new as any;
            toast.success(`New testimonial from ${newTestimonial.name}!`, {
              description: `${newTestimonial.rating} star review from ${newTestimonial.company}`,
              duration: 5000,
            });
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);

  // Create testimonial
  const createTestimonialMutation = useMutation({
    mutationFn: async (testimonial: any) => {
      const { error } = await supabase
        .from("testimonials")
        .insert({
          ...testimonial,
          status: testimonial.status || "pending",
        });

      if (error) throw error;
      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["testimonials"] });
      toast.success("Testimonial created successfully!");
    },
    onError: (error) => {
      console.error("Error creating testimonial:", error);
      toast.error("Failed to create testimonial");
    },
  });

  // Update testimonial
  const updateTestimonialMutation = useMutation({
    mutationFn: async ({ id, ...updates }: any) => {
      const { data, error } = await supabase
        .from("testimonials")
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["testimonials"] });
      toast.success("Testimonial updated successfully!");
    },
    onError: (error) => {
      console.error("Error updating testimonial:", error);
      toast.error("Failed to update testimonial");
    },
  });

  // Delete testimonial
  const deleteTestimonialMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from("testimonials")
        .delete()
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["testimonials"] });
      toast.success("Testimonial deleted successfully!");
    },
    onError: (error) => {
      console.error("Error deleting testimonial:", error);
      toast.error("Failed to delete testimonial");
    },
  });

  return {
    testimonials,
    isLoading,
    error,
    createTestimonial: createTestimonialMutation.mutate,
    updateTestimonial: updateTestimonialMutation.mutate,
    deleteTestimonial: deleteTestimonialMutation.mutate,
    isCreating: createTestimonialMutation.isPending,
    isUpdating: updateTestimonialMutation.isPending,
    isDeleting: deleteTestimonialMutation.isPending,
  };
};
