-- Setup testimonial image storage bucket
-- Run this in Supabase SQL Editor

-- Create storage bucket for testimonial images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'testimonial-images',
  'testimonial-images',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- Create policy to allow public read access
CREATE POLICY "Public read access for testimonial images" ON storage.objects
FOR SELECT USING (bucket_id = 'testimonial-images');

-- Create policy to allow anyone to upload testimonial images (for public testimonial submission)
CREATE POLICY "Allow anyone to upload testimonial images" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'testimonial-images'
);

-- Create policy to allow users to update their own uploads
CREATE POLICY "Allow users to update their own testimonial images" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'testimonial-images' AND
  auth.role() = 'authenticated'
);

-- Create policy to allow admins to delete testimonial images
CREATE POLICY "Allow admins to delete testimonial images" ON storage.objects
FOR DELETE USING (
  bucket_id = 'testimonial-images' AND
  EXISTS (
    SELECT 1 FROM admin_users au 
    WHERE au.id = auth.uid() AND au.is_active = true
  )
);
