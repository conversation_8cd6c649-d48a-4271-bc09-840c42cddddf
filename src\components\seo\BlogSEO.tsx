import React from 'react';
import { Helmet } from 'react-helmet-async';
import { seoConfig } from '../../config/seo';

interface BlogSEOProps {
  // Blog post specific props
  title?: string;
  description?: string;
  slug?: string;
  content?: string;
  excerpt?: string;
  featuredImage?: string;
  publishedAt?: string;
  modifiedAt?: string;
  author?: string;
  categories?: Array<{ name: string; slug: string }>;
  tags?: string[];
  readingTime?: number;
  
  // Page type (blog listing vs individual post)
  pageType?: 'blog-home' | 'blog-post';
}

const BlogSEO: React.FC<BlogSEOProps> = ({
  title,
  description,
  slug,
  content,
  excerpt,
  featuredImage,
  publishedAt,
  modifiedAt,
  author = seoConfig.name,
  categories = [],
  tags = [],
  readingTime,
  pageType = 'blog-home'
}) => {
  // Generate SEO data based on page type
  const generateSEOData = () => {
    if (pageType === 'blog-post' && slug) {
      // Individual blog post SEO
      const postTitle = title ? 
        `${title} | ${seoConfig.name} Blog` : 
        `Blog Post | ${seoConfig.name}`;
      
      const postDescription = description || excerpt || 
        `Read this insightful blog post by ${seoConfig.name}, Full Stack Developer from Bursa, Turkey.`;
      
      const postUrl = `${seoConfig.url}/blog/${slug}`;
      const postImage = featuredImage || `${seoConfig.url}${seoConfig.images.ogImage}`;
      
      const postKeywords = [
        ...seoConfig.blog.tags,
        ...tags,
        ...categories.map(cat => cat.name.toLowerCase()),
        'blog post',
        'tutorial',
        'web development',
        'programming',
        'turkey developer blog'
      ].join(', ');

      return {
        title: postTitle,
        description: postDescription,
        url: postUrl,
        image: postImage,
        keywords: postKeywords,
        type: 'article'
      };
    } else {
      // Blog home page SEO
      const blogPageSEO = seoConfig.pages.blog;
      return {
        title: blogPageSEO.title,
        description: blogPageSEO.description,
        url: `${seoConfig.url}/blog`,
        image: `${seoConfig.url}${seoConfig.images.ogImage}`,
        keywords: blogPageSEO.keywords,
        type: 'website'
      };
    }
  };

  const seoData = generateSEOData();

  // Generate structured data for blog posts
  const generateStructuredData = () => {
    if (pageType === 'blog-post' && slug) {
      return {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "headline": title,
        "description": description || excerpt,
        "image": featuredImage || `${seoConfig.url}${seoConfig.images.ogImage}`,
        "author": {
          "@type": "Person",
          "name": author,
          "url": seoConfig.url,
          "sameAs": [
            seoConfig.social.github,
            seoConfig.social.linkedin
          ]
        },
        "publisher": {
          "@type": "Person",
          "name": seoConfig.name,
          "logo": {
            "@type": "ImageObject",
            "url": `${seoConfig.url}${seoConfig.images.logo}`
          }
        },
        "datePublished": publishedAt,
        "dateModified": modifiedAt || publishedAt,
        "mainEntityOfPage": {
          "@type": "WebPage",
          "@id": seoData.url
        },
        "url": seoData.url,
        "wordCount": content ? content.split(' ').length : undefined,
        "timeRequired": readingTime ? `PT${readingTime}M` : undefined,
        "keywords": tags.join(', '),
        "articleSection": categories.map(cat => cat.name),
        "inLanguage": "en-US",
        "isAccessibleForFree": true
      };
    } else {
      return {
        "@context": "https://schema.org",
        "@type": "Blog",
        "name": `${seoConfig.name} Blog`,
        "description": seoData.description,
        "url": seoData.url,
        "author": {
          "@type": "Person",
          "name": seoConfig.name,
          "url": seoConfig.url
        },
        "publisher": {
          "@type": "Person",
          "name": seoConfig.name
        },
        "inLanguage": "en-US",
        "about": seoConfig.blog.categories
      };
    }
  };

  const structuredData = generateStructuredData();

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{seoData.title}</title>
      <meta name="description" content={seoData.description} />
      <meta name="keywords" content={seoData.keywords} />
      <meta name="author" content={author} />
      <link rel="canonical" href={seoData.url} />

      {/* Open Graph */}
      <meta property="og:type" content={seoData.type} />
      <meta property="og:title" content={seoData.title} />
      <meta property="og:description" content={seoData.description} />
      <meta property="og:image" content={seoData.image} />
      <meta property="og:url" content={seoData.url} />
      <meta property="og:site_name" content={`${seoConfig.name} Portfolio`} />
      <meta property="og:locale" content="en_US" />
      
      {/* Article specific Open Graph tags */}
      {pageType === 'blog-post' && (
        <>
          <meta property="article:author" content={author} />
          <meta property="article:published_time" content={publishedAt} />
          {modifiedAt && <meta property="article:modified_time" content={modifiedAt} />}
          {categories.map(category => (
            <meta key={category.slug} property="article:section" content={category.name} />
          ))}
          {tags.map(tag => (
            <meta key={tag} property="article:tag" content={tag} />
          ))}
        </>
      )}

      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={seoData.title} />
      <meta name="twitter:description" content={seoData.description} />
      <meta name="twitter:image" content={seoData.image} />
      <meta name="twitter:creator" content={seoConfig.social.twitter} />
      <meta name="twitter:site" content={seoConfig.social.twitter} />

      {/* Additional SEO */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow" />
      
      {/* Blog specific meta tags */}
      {pageType === 'blog-post' && readingTime && (
        <meta name="twitter:label1" content="Reading time" />
      )}
      {pageType === 'blog-post' && readingTime && (
        <meta name="twitter:data1" content={`${readingTime} min read`} />
      )}

      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
    </Helmet>
  );
};

export default BlogSEO;
