-- Update existing data from InBio/<PERSON>/LiftDev to <PERSON><PERSON><PERSON>
-- Run this script in your Supabase SQL Editor to update existing data

-- Update hero content
UPDATE portfolio_content 
SET content = jsonb_set(
  jsonb_set(
    jsonb_set(
      jsonb_set(
        jsonb_set(
          jsonb_set(
            content,
            '{name}', '"Kenenis Dev"'
          ),
          '{profession}', '"Full Stack Developer"'
        ),
        '{welcomeText}', '"WELCOME TO MY WORLD"'
      ),
      '{description}', '"I''m a passionate full stack developer specializing in building exceptional digital experiences. Currently, I''m focused on building accessible, human-centered products."'
    ),
    '{buttonText}', '"More About Me"'
  ),
  '{typingTexts}', '["Full Stack Developer", "UI/UX Designer", "Web Developer"]'
)
WHERE section = 'hero';

-- Update about content if it exists
UPDATE portfolio_content 
SET content = jsonb_set(
  content,
  '{title}', '"About Me"'
)
WHERE section = 'about';

-- Update site settings
UPDATE site_settings
SET value = jsonb_set(
  jsonb_set(
    jsonb_set(
      value,
      '{siteName}', '"Kenenis Dev Portfolio"'
    ),
    '{contactEmail}', '"<EMAIL>"'
  ),
  '{language}', '"en"'
)
WHERE key = 'general';

-- Remove timezone field if it exists
UPDATE site_settings
SET value = value - 'timezone'
WHERE key = 'general' AND value ? 'timezone';

-- Update contact information if it exists
UPDATE portfolio_content 
SET content = jsonb_set(
  jsonb_set(
    jsonb_set(
      content,
      '{email}', '"<EMAIL>"'
    ),
    '{phone}', '"+****************"'
  ),
  '{address}', '"Remote Developer"'
)
WHERE section = 'contact';

-- Update any testimonials that mention John/John Smith/LiftDev
UPDATE testimonials 
SET message = REPLACE(
  REPLACE(
    REPLACE(
      REPLACE(
        REPLACE(message, 'John Smith', 'Kenenis'),
        'John was', 'Kenenis was'
      ),
      'John ', 'Kenenis '
    ),
    'LiftDev was', 'Kenenis was'
  ),
  'LiftDev', 'Kenenis'
)
WHERE message LIKE '%John%' OR message LIKE '%LiftDev%';

-- Success message
SELECT 'Successfully updated all data to Kenenis Dev!' as message;
