-- Blog Posts Database Setup Script
-- Run this in your Supabase SQL Editor to add blog functionality

-- Create blog_posts table
CREATE TABLE IF NOT EXISTS blog_posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    excerpt TEXT,
    content TEXT NOT NULL,
    featured_image TEXT,
    tags TEXT[] DEFAULT '{}',
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    author_id UUID REFERENCES admin_users(id) ON DELETE SET NULL,
    published_at TIMESTAMP WITH TIME ZONE,
    reading_time INTEGER DEFAULT 0, -- in minutes
    views_count INTEGER DEFAULT 0,
    likes_count INTEGER DEFAULT 0,
    meta_title TEXT,
    meta_description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create blog_categories table
CREATE TABLE IF NOT EXISTS blog_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    color TEXT DEFAULT '#ef4444', -- Default red color
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create blog_post_categories junction table (many-to-many)
CREATE TABLE IF NOT EXISTS blog_post_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
    category_id UUID REFERENCES blog_categories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(post_id, category_id)
);

-- Create blog_comments table
CREATE TABLE IF NOT EXISTS blog_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
    author_name TEXT NOT NULL,
    author_email TEXT NOT NULL,
    content TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_blog_posts_status ON blog_posts(status);
CREATE INDEX IF NOT EXISTS idx_blog_posts_published_at ON blog_posts(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON blog_posts(slug);
CREATE INDEX IF NOT EXISTS idx_blog_posts_author ON blog_posts(author_id);
CREATE INDEX IF NOT EXISTS idx_blog_posts_tags ON blog_posts USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_blog_comments_post ON blog_comments(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_status ON blog_comments(status);

-- Enable RLS on blog tables
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_post_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_comments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for blog_posts
-- Public can read published posts
CREATE POLICY "Anyone can view published blog posts" ON blog_posts FOR SELECT USING (
    status = 'published'
);

-- Admins can do everything with blog posts
CREATE POLICY "Admins can manage blog posts" ON blog_posts FOR ALL USING (
    EXISTS (SELECT 1 FROM admin_users WHERE id = auth.uid() AND is_active = true)
);

-- Create RLS policies for blog_categories
-- Public can read categories
CREATE POLICY "Anyone can view blog categories" ON blog_categories FOR SELECT USING (true);

-- Admins can manage categories
CREATE POLICY "Admins can manage blog categories" ON blog_categories FOR ALL USING (
    EXISTS (SELECT 1 FROM admin_users WHERE id = auth.uid() AND is_active = true)
);

-- Create RLS policies for blog_post_categories
-- Public can read post-category relationships
CREATE POLICY "Anyone can view blog post categories" ON blog_post_categories FOR SELECT USING (true);

-- Admins can manage post-category relationships
CREATE POLICY "Admins can manage blog post categories" ON blog_post_categories FOR ALL USING (
    EXISTS (SELECT 1 FROM admin_users WHERE id = auth.uid() AND is_active = true)
);

-- Create RLS policies for blog_comments
-- Public can read approved comments
CREATE POLICY "Anyone can view approved comments" ON blog_comments FOR SELECT USING (
    status = 'approved'
);

-- Public can insert comments (for contact form-like functionality)
CREATE POLICY "Anyone can create comments" ON blog_comments FOR INSERT WITH CHECK (true);

-- Admins can manage all comments
CREATE POLICY "Admins can manage comments" ON blog_comments FOR ALL USING (
    EXISTS (SELECT 1 FROM admin_users WHERE id = auth.uid() AND is_active = true)
);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_blog_posts_updated_at BEFORE UPDATE ON blog_posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_categories_updated_at BEFORE UPDATE ON blog_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_comments_updated_at BEFORE UPDATE ON blog_comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to generate slug from title
CREATE OR REPLACE FUNCTION generate_slug(title TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN lower(regexp_replace(regexp_replace(title, '[^a-zA-Z0-9\s-]', '', 'g'), '\s+', '-', 'g'));
END;
$$ LANGUAGE plpgsql;

-- Function to calculate reading time (assuming 200 words per minute)
CREATE OR REPLACE FUNCTION calculate_reading_time(content TEXT)
RETURNS INTEGER AS $$
BEGIN
    RETURN GREATEST(1, (array_length(string_to_array(content, ' '), 1) / 200.0)::INTEGER);
END;
$$ LANGUAGE plpgsql;

-- Function to increment blog post view count
CREATE OR REPLACE FUNCTION increment_blog_views(post_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE blog_posts
    SET views_count = views_count + 1
    WHERE id = post_id;
END;
$$ LANGUAGE plpgsql;

-- Insert sample blog categories
INSERT INTO blog_categories (name, slug, description, color) VALUES
('Web Development', 'web-development', 'Articles about web development, frameworks, and best practices', '#3b82f6'),
('Design', 'design', 'UI/UX design principles, trends, and tutorials', '#8b5cf6'),
('Technology', 'technology', 'Latest technology trends and innovations', '#10b981'),
('Tutorial', 'tutorial', 'Step-by-step guides and tutorials', '#f59e0b'),
('Personal', 'personal', 'Personal thoughts and experiences', '#ef4444')
ON CONFLICT (slug) DO NOTHING;

-- Insert sample blog post
INSERT INTO blog_posts (
    title, 
    slug, 
    excerpt, 
    content, 
    status, 
    published_at,
    tags,
    meta_title,
    meta_description
) VALUES (
    'Welcome to My Blog',
    'welcome-to-my-blog',
    'This is the first post on my new blog. Here I''ll share my thoughts on web development, design, and technology.',
    '# Welcome to My Blog

This is my first blog post! I''m excited to share my journey in web development and design with you.

## What You Can Expect

- **Web Development Tips**: Best practices, tutorials, and insights
- **Design Inspiration**: UI/UX trends and creative processes  
- **Technology Reviews**: Latest tools and frameworks
- **Personal Projects**: Behind-the-scenes of my work

## Let''s Connect

I''d love to hear from you! Feel free to reach out through the contact form or connect with me on social media.

Happy coding! 🚀',
    'published',
    NOW(),
    ARRAY['welcome', 'introduction', 'web-development'],
    'Welcome to My Blog - Web Development & Design',
    'Welcome to my blog where I share insights about web development, design, and technology. Join me on this journey!'
) ON CONFLICT (slug) DO NOTHING;

-- Success message
SELECT 'Blog database setup completed successfully!' as message;
