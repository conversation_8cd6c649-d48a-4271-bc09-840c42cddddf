import React, { useState } from 'react';
import { 
  MessageSquare, 
  Send, 
  User, 
  Calendar,
  Mail,
  AlertCircle
} from 'lucide-react';
import { useBlogComments } from '../../hooks/useBlogComments';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { toast } from 'sonner';

interface BlogCommentsProps {
  postId: string;
  postTitle: string;
}

const BlogComments: React.FC<BlogCommentsProps> = ({ postId, postTitle }) => {
  const { comments, isLoading, createComment, isCreating } = useBlogComments(postId);
  
  const [formData, setFormData] = useState({
    author_name: '',
    author_email: '',
    content: '',
  });

  const [showForm, setShowForm] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.author_name.trim()) {
      toast.error('Name is required');
      return;
    }
    
    if (!formData.author_email.trim()) {
      toast.error('Email is required');
      return;
    }
    
    if (!formData.content.trim()) {
      toast.error('Comment is required');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.author_email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    try {
      await createComment({
        post_id: postId,
        author_name: formData.author_name.trim(),
        author_email: formData.author_email.trim(),
        content: formData.content.trim(),
      });
      
      // Reset form
      setFormData({
        author_name: '',
        author_email: '',
        content: '',
      });
      setShowForm(false);
    } catch (error) {
      console.error('Error submitting comment:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <MessageSquare className="w-5 h-5" />
          <h3 className="text-xl font-semibold">Comments</h3>
        </div>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
          <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <MessageSquare className="w-5 h-5 text-red" />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Comments ({comments?.length || 0})
          </h3>
        </div>
        
        {!showForm && (
          <Button
            onClick={() => setShowForm(true)}
            className="bg-red hover:bg-red/80"
          >
            <MessageSquare className="w-4 h-4 mr-2" />
            Add Comment
          </Button>
        )}
      </div>

      {/* Comment Form */}
      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Send className="w-5 h-5" />
              Leave a Comment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="author_name">Name *</Label>
                  <Input
                    id="author_name"
                    type="text"
                    value={formData.author_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, author_name: e.target.value }))}
                    placeholder="Your name"
                    className="mt-1"
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="author_email">Email *</Label>
                  <Input
                    id="author_email"
                    type="email"
                    value={formData.author_email}
                    onChange={(e) => setFormData(prev => ({ ...prev, author_email: e.target.value }))}
                    placeholder="<EMAIL>"
                    className="mt-1"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Your email will not be published
                  </p>
                </div>
              </div>
              
              <div>
                <Label htmlFor="content">Comment *</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="Share your thoughts..."
                  rows={4}
                  className="mt-1"
                  required
                />
              </div>


              
              <div className="flex gap-2">
                <Button
                  type="submit"
                  disabled={isCreating}
                  className="bg-red hover:bg-red/80"
                >
                  {isCreating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4 mr-2" />
                      Submit Comment
                    </>
                  )}
                </Button>
                
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowForm(false)}
                  disabled={isCreating}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Comments List */}
      {comments && comments.length > 0 ? (
        <div className="space-y-4">
          {comments.map((comment) => (
            <Card key={comment.id} className="border-l-4 border-l-red">
              <CardContent className="pt-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-red/10 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-red" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {comment.author_name}
                      </h4>
                      <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                        <Calendar className="w-3 h-3" />
                        {formatDate(comment.created_at)}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="prose prose-sm dark:prose-invert max-w-none">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {comment.content}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No comments yet
          </h4>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Be the first to share your thoughts on "{postTitle}"
          </p>
          {!showForm && (
            <Button
              onClick={() => setShowForm(true)}
              className="bg-red hover:bg-red/80"
            >
              <MessageSquare className="w-4 h-4 mr-2" />
              Start the Discussion
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default BlogComments;
