import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../services/supabase/client';
import { toast } from 'sonner';
import type { Database } from '../services/supabase/types';

type BlogComment = Database['public']['Tables']['blog_comments']['Row'];
type BlogCommentInsert = Database['public']['Tables']['blog_comments']['Insert'];
type BlogCommentUpdate = Database['public']['Tables']['blog_comments']['Update'];

export const useBlogComments = (postId?: string) => {
  const queryClient = useQueryClient();

  // Fetch approved comments for a specific post
  const {
    data: comments,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['blog-comments', postId],
    queryFn: async () => {
      if (!postId) return [];
      
      const { data, error } = await supabase
        .from('blog_comments')
        .select('*')
        .eq('post_id', postId)
        .eq('status', 'approved')
        .order('created_at', { ascending: true });

      if (error) throw error;
      return data as BlogComment[];
    },
    enabled: !!postId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch all comments for admin (all statuses)
  const {
    data: allComments,
    isLoading: isLoadingAll,
    error: errorAll,
  } = useQuery({
    queryKey: ['blog-comments-all', postId],
    queryFn: async () => {
      if (!postId) return [];
      
      const { data, error } = await supabase
        .from('blog_comments')
        .select('*')
        .eq('post_id', postId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as BlogComment[];
    },
    enabled: !!postId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Create comment mutation
  const createCommentMutation = useMutation({
    mutationFn: async (commentData: BlogCommentInsert) => {
      const { data, error } = await supabase
        .from('blog_comments')
        .insert({
          ...commentData,
          status: 'approved' // Immediately approve comments
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blog-comments', postId] });
      queryClient.invalidateQueries({ queryKey: ['blog-comments-all', postId] });
      toast.success('Comment successfully added!');
    },
    onError: (error) => {
      console.error('Error creating comment:', error);
      toast.error('Failed to submit comment');
    },
  });

  // Update comment status mutation (admin only)
  const updateCommentMutation = useMutation({
    mutationFn: async ({ id, status }: { id: string; status: 'approved' | 'rejected' }) => {
      const { data, error } = await supabase
        .from('blog_comments')
        .update({ status })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (_, { status }) => {
      queryClient.invalidateQueries({ queryKey: ['blog-comments', postId] });
      queryClient.invalidateQueries({ queryKey: ['blog-comments-all', postId] });
      const message = status === 'approved' ? 'Comment published!' : 'Comment hidden!';
      toast.success(message);
    },
    onError: (error) => {
      console.error('Error updating comment:', error);
      toast.error('Failed to update comment');
    },
  });

  // Delete comment mutation (admin only)
  const deleteCommentMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('blog_comments')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blog-comments', postId] });
      queryClient.invalidateQueries({ queryKey: ['blog-comments-all', postId] });
      toast.success('Comment deleted!');
    },
    onError: (error) => {
      console.error('Error deleting comment:', error);
      toast.error('Failed to delete comment');
    },
  });

  return {
    // Data
    comments,
    allComments,
    
    // Loading states
    isLoading,
    isLoadingAll,
    
    // Errors
    error,
    errorAll,
    
    // Mutations
    createComment: createCommentMutation.mutate,
    updateComment: updateCommentMutation.mutate,
    deleteComment: deleteCommentMutation.mutate,
    
    // Mutation states
    isCreating: createCommentMutation.isPending,
    isUpdating: updateCommentMutation.isPending,
    isDeleting: deleteCommentMutation.isPending,
  };
};
