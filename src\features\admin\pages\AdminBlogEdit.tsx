import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import AdminLayout from '../components/AdminLayout';
import BlogPostForm from '../components/BlogPostForm';
import { useBlogPosts } from '../../../hooks/useBlog';
import type { BlogPostWithCategories } from '../../../hooks/useBlog';

const AdminBlogEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { allPosts, isLoadingAll } = useBlogPosts();
  const [post, setPost] = useState<BlogPostWithCategories | null>(null);

  useEffect(() => {
    if (id && allPosts) {
      const foundPost = allPosts.find(p => p.id === id);
      setPost(foundPost || null);
    }
  }, [id, allPosts]);

  if (isLoadingAll) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!post) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Post Not Found
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            The blog post you're trying to edit doesn't exist.
          </p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <BlogPostForm post={post} />
    </AdminLayout>
  );
};

export default AdminBlogEdit;
