<svg width="1200" height="630" viewBox="0 0 1200 630" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1e293b" stop-opacity="1" />
      <stop offset="100%" stop-color="#0f172a" stop-opacity="1" />
    </linearGradient>
    <linearGradient id="k-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ff014f" stop-opacity="1" />
      <stop offset="100%" stop-color="#f9004d" stop-opacity="1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect x="0" y="0" width="1200" height="630" fill="url(#bg-gradient)"/>
  
  <!-- Logo container centered -->
  <g transform="translate(400, 115)">
    <!-- Modern K letter -->
    <g transform="scale(1.5)">
      <!-- Left vertical line of K -->
      <rect x="0" y="0" width="54" height="312" fill="url(#k-gradient)" rx="27"/>
      
      <!-- Upper diagonal of K -->
      <path d="M70 0 L256 156 L218 194 L70 86 Z" fill="url(#k-gradient)"/>
      
      <!-- Lower diagonal of K -->
      <path d="M70 226 L218 118 L256 156 L70 312 Z" fill="url(#k-gradient)"/>
    </g>
  </g>
  
  <!-- Text -->
  <text x="600" y="480" text-anchor="middle" fill="white" font-family="Arial, Helvetica, sans-serif" font-size="48" font-weight="bold">
    Kenenisa Kero (Kenenis)
  </text>
  <text x="600" y="530" text-anchor="middle" fill="#94a3b8" font-family="Arial, Helvetica, sans-serif" font-size="32" font-weight="normal">
    Full Stack Developer &amp; UI/UX Designer
  </text>
  <text x="600" y="570" text-anchor="middle" fill="#64748b" font-family="Arial, Helvetica, sans-serif" font-size="24" font-weight="normal">
    React • Node.js • TypeScript • Bursa, Turkey
  </text>
  
  <!-- Subtle glow effect -->
  <rect x="8" y="8" width="1184" height="614" rx="20" ry="20" fill="none" stroke="url(#k-gradient)" stroke-width="4" opacity="0.3"/>
</svg>
