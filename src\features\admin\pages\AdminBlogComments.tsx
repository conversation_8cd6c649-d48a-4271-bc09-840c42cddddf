import React, { useState } from 'react';
import {
  MessageSquare,
  Check,
  X,
  Trash2,
  Search,
  Filter,
  Calendar,
  User,
  Mail,
  Clock
} from 'lucide-react';
import AdminLayout from '../components/AdminLayout';
import { useBlogComments } from '../../../hooks/useBlogComments';
import { useBlogPosts } from '../../../hooks/useBlog';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Badge } from '../../../components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../../../components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/dialog';

const AdminBlogComments: React.FC = () => {
  const { allPosts } = useBlogPosts();
  const [selectedPostId, setSelectedPostId] = useState<string>('');
  const { allComments, isLoadingAll, updateComment, deleteComment, isUpdating, isDeleting } = useBlogComments(selectedPostId);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [commentToDelete, setCommentToDelete] = useState<any>(null);

  // Filter comments
  const filteredComments = allComments?.filter(comment => {
    const matchesSearch = comment.author_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         comment.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         comment.author_email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || comment.status === statusFilter;
    return matchesSearch && matchesStatus;
  }) || [];

  // Calculate stats
  const stats = {
    total: allComments?.length || 0,
    pending: allComments?.filter(c => c.status === 'pending').length || 0,
    approved: allComments?.filter(c => c.status === 'approved').length || 0,
    rejected: allComments?.filter(c => c.status === 'rejected').length || 0,
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'rejected':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'approved':
        return 'Published';
      case 'pending':
        return 'Pending Review';
      case 'rejected':
        return 'Hidden';
      default:
        return status;
    }
  };

  const handleDeleteComment = async () => {
    if (!commentToDelete) return;
    
    try {
      await deleteComment(commentToDelete.id);
      setDeleteDialogOpen(false);
      setCommentToDelete(null);
    } catch (error) {
      console.error('Error deleting comment:', error);
    }
  };

  if (isLoadingAll && selectedPostId) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-4 sm:p-6">
        {/* Header */}
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Comment Management</h1>
          <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">Moderate and manage blog comments</p>
        </div>

        {/* Post Selection */}
        <Card>
          <CardHeader className="pb-3 sm:pb-6">
            <CardTitle className="text-lg sm:text-xl">Select Blog Post</CardTitle>
          </CardHeader>
          <CardContent className="p-4 sm:p-6">
            <select
              value={selectedPostId}
              onChange={(e) => setSelectedPostId(e.target.value)}
              className="w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-red"
            >
              <option value="">Select a blog post...</option>
              {allPosts?.map((post) => (
                <option key={post.id} value={post.id}>
                  {post.title}
                </option>
              ))}
            </select>
          </CardContent>
        </Card>

        {selectedPostId && (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-3 sm:p-6">
                  <CardTitle className="text-xs sm:text-sm font-medium">Total Comments</CardTitle>
                  <MessageSquare className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent className="p-3 sm:p-6 pt-0">
                  <div className="text-lg sm:text-2xl font-bold">{stats.total}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-3 sm:p-6">
                  <CardTitle className="text-xs sm:text-sm font-medium">Published</CardTitle>
                  <Check className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent className="p-3 sm:p-6 pt-0">
                  <div className="text-lg sm:text-2xl font-bold text-green-600">{stats.approved}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-3 sm:p-6">
                  <CardTitle className="text-xs sm:text-sm font-medium">Pending Review</CardTitle>
                  <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent className="p-3 sm:p-6 pt-0">
                  <div className="text-lg sm:text-2xl font-bold text-yellow-600">{stats.pending}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-3 sm:p-6">
                  <CardTitle className="text-xs sm:text-sm font-medium">Hidden</CardTitle>
                  <X className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent className="p-3 sm:p-6 pt-0">
                  <div className="text-lg sm:text-2xl font-bold text-orange-600">{stats.rejected}</div>
                </CardContent>
              </Card>
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  type="text"
                  placeholder="Search comments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 text-sm sm:text-base"
                />
              </div>

              <div className="flex items-center gap-2 sm:min-w-[200px]">
                <Filter className="w-4 h-4 text-gray-500" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-red flex-1 sm:flex-none"
                >
                  <option value="">All Status</option>
                  <option value="pending">Pending Review</option>
                  <option value="approved">Published</option>
                  <option value="rejected">Hidden</option>
                </select>
              </div>
            </div>

            {/* Comments Table - Desktop */}
            <Card className="hidden lg:block">
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Author</TableHead>
                        <TableHead>Comment</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                  <TableBody>
                    {filteredComments.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8">
                          <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                          <p className="text-gray-500">No comments found</p>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredComments.map((comment) => (
                        <TableRow key={comment.id}>
                          <TableCell>
                            <div>
                              <div className="flex items-center gap-2">
                                <User className="w-4 h-4 text-gray-400" />
                                <span className="font-medium">{comment.author_name}</span>
                              </div>
                              <div className="flex items-center gap-2 text-sm text-gray-500">
                                <Mail className="w-3 h-3" />
                                <span>{comment.author_email}</span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="max-w-xs truncate">
                              {comment.content}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(comment.status)}>
                              {getStatusLabel(comment.status)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {formatDate(comment.created_at)}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              {comment.status === 'approved' && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => updateComment({ id: comment.id, status: 'rejected' })}
                                  disabled={isUpdating}
                                  className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                                  title="Hide Comment"
                                >
                                  <X className="w-4 h-4" />
                                </Button>
                              )}
                              {comment.status === 'rejected' && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => updateComment({ id: comment.id, status: 'approved' })}
                                  disabled={isUpdating}
                                  className="text-green-600 hover:text-green-700 hover:bg-green-50"
                                  title="Show Comment"
                                >
                                  <Check className="w-4 h-4" />
                                </Button>
                              )}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setCommentToDelete(comment);
                                  setDeleteDialogOpen(true);
                                }}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                title="Delete Comment"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>

            {/* Comments Cards - Mobile */}
            <div className="lg:hidden space-y-4">
              {filteredComments.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-8">
                    <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">No comments found</p>
                  </CardContent>
                </Card>
              ) : (
                filteredComments.map((comment) => (
                  <Card key={comment.id} className="border-l-4 border-l-red">
                    <CardContent className="p-4">
                      {/* Author Info */}
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <User className="w-4 h-4 text-gray-400" />
                            <span className="font-medium text-sm">{comment.author_name}</span>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-gray-500">
                            <Mail className="w-3 h-3" />
                            <span className="truncate">{comment.author_email}</span>
                          </div>
                        </div>
                        <Badge className={`${getStatusColor(comment.status)} text-xs`}>
                          {getStatusLabel(comment.status)}
                        </Badge>
                      </div>

                      {/* Comment Content */}
                      <div className="mb-3">
                        <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                          {comment.content}
                        </p>
                      </div>

                      {/* Date and Actions */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <Calendar className="w-3 h-3" />
                          {formatDate(comment.created_at)}
                        </div>

                        <div className="flex items-center gap-2">
                          {comment.status === 'approved' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => updateComment({ id: comment.id, status: 'rejected' })}
                              disabled={isUpdating}
                              className="text-orange-600 hover:text-orange-700 hover:bg-orange-50 h-8 w-8 p-0"
                              title="Hide Comment"
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          )}
                          {comment.status === 'rejected' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => updateComment({ id: comment.id, status: 'approved' })}
                              disabled={isUpdating}
                              className="text-green-600 hover:text-green-700 hover:bg-green-50 h-8 w-8 p-0"
                              title="Show Comment"
                            >
                              <Check className="w-4 h-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setCommentToDelete(comment);
                              setDeleteDialogOpen(true);
                            }}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50 h-8 w-8 p-0"
                            title="Delete Comment"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </>
        )}

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Comment</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this comment from "{commentToDelete?.author_name}"? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setDeleteDialogOpen(false)}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteComment}
                disabled={isDeleting}
              >
                {isDeleting ? 'Deleting...' : 'Delete'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  );
};

export default AdminBlogComments;
