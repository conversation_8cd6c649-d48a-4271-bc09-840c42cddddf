import React from 'react';
import { useFallbackDataSettings } from '../../hooks/useSiteSettings';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Database, HardDrive } from 'lucide-react';

const FallbackDataDemo: React.FC = () => {
  const { enableFallbackData } = useFallbackDataSettings();

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {enableFallbackData ? (
            <HardDrive className="w-5 h-5 text-blue-500" />
          ) : (
            <Database className="w-5 h-5 text-green-500" />
          )}
          Fallback Data Status
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Current Mode:</span>
            <Badge 
              variant={enableFallbackData ? "secondary" : "default"}
              className={enableFallbackData ? "bg-blue-100 text-blue-800" : "bg-green-100 text-green-800"}
            >
              {enableFallbackData ? "Fallback Enabled" : "Database Only"}
            </Badge>
          </div>
          
          <div className="text-sm text-gray-600 dark:text-gray-300">
            {enableFallbackData ? (
              <>
                <p className="mb-2">✅ <strong>Fallback data is enabled</strong></p>
                <p>• Portfolio will show hardcoded items if database is empty</p>
                <p>• Testimonials will display sample testimonials as fallback</p>
                <p>• Content sections will use default text when database content is unavailable</p>
              </>
            ) : (
              <>
                <p className="mb-2">🚫 <strong>Fallback data is disabled</strong></p>
                <p>• Portfolio will be empty if no database items exist</p>
                <p>• Testimonials will be hidden if no approved testimonials</p>
                <p>• Content sections will be empty until database content loads</p>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default FallbackDataDemo;
