import React, { useEffect, useState } from "react";
import { supabase } from "../../services/supabase/client";
import { toast } from "sonner";
import { RefreshCw } from "lucide-react";

const RealtimeNotification = () => {
  const [isConnected, setIsConnected] = useState(true);

  useEffect(() => {
    // Listen for all portfolio_content changes
    const channel = supabase
      .channel("content_updates")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "portfolio_content",
        },
        (payload) => {
          
          if (payload.eventType === "UPDATE") {
            const section = payload.new?.section || "content";
            toast.success(
              `${section.charAt(0).toUpperCase() + section.slice(1)} section updated!`,
              {
                icon: <RefreshCw className="w-4 h-4 animate-spin" />,
                duration: 3000,
              }
            );
          } else if (payload.eventType === "INSERT") {
            const section = payload.new?.section || "content";
            toast.info(
              `New ${section} section created!`,
              {
                icon: <RefreshCw className="w-4 h-4" />,
                duration: 3000,
              }
            );
          }
        }
      )
      .subscribe((status) => {
        setIsConnected(status === "SUBSCRIBED");
      });

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  // Don't render anything - this is just for notifications
  return null;
};

export default RealtimeNotification;
