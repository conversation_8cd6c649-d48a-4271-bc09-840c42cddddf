import { useState, useEffect } from 'react';
import { toast } from 'sonner';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

interface PWAState {
  isInstallable: boolean;
  isInstalled: boolean;
  isOnline: boolean;
  isUpdateAvailable: boolean;
  installPrompt: BeforeInstallPromptEvent | null;
}

export const usePWA = () => {
  const [pwaState, setPwaState] = useState<PWAState>({
    isInstallable: false,
    isInstalled: false,
    isOnline: navigator.onLine,
    isUpdateAvailable: false,
    installPrompt: null,
  });

  // Register service worker
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          // Check for updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  setPwaState(prev => ({ ...prev, isUpdateAvailable: true }));
                  toast.info('New version available!', {
                    description: 'Refresh to get the latest updates.',
                    action: {
                      label: 'Refresh',
                      onClick: () => window.location.reload(),
                    },
                  });
                }
              });
            }
          });
        })
        .catch((error) => {
          console.warn('Service worker registration failed:', error);
        });
    }
  }, []);

  // Handle install prompt - Let browser handle natively
  useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      // Don't prevent default - let browser show native install prompt
      // e.preventDefault();
      const installEvent = e as BeforeInstallPromptEvent;
      setPwaState(prev => ({
        ...prev,
        isInstallable: true,
        installPrompt: installEvent,
      }));
    };

    const handleAppInstalled = () => {
      setPwaState(prev => ({
        ...prev,
        isInstalled: true,
        isInstallable: false,
        installPrompt: null,
      }));
      toast.success('Portfolio installed successfully!', {
        description: 'You can now access it from your home screen.',
      });
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  // Handle online/offline status
  useEffect(() => {
    const handleOnline = () => {
      setPwaState(prev => ({ ...prev, isOnline: true }));
      toast.success('Back online!', {
        description: 'Connection restored.',
      });
    };

    const handleOffline = () => {
      setPwaState(prev => ({ ...prev, isOnline: false }));
      toast.warning('You are offline', {
        description: 'Some features may be limited.',
      });
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Check if already installed
  useEffect(() => {
    const checkIfInstalled = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isInWebAppiOS = (window.navigator as any).standalone === true;
      const isInstalled = isStandalone || isInWebAppiOS;
      
      setPwaState(prev => ({ ...prev, isInstalled }));
    };

    checkIfInstalled();
  }, []);

  // Install PWA
  const installPWA = async () => {
    if (!pwaState.installPrompt) {
      toast.error('Installation not available', {
        description: 'This device does not support PWA installation.',
      });
      return;
    }

    try {
      await pwaState.installPrompt.prompt();
      const { outcome } = await pwaState.installPrompt.userChoice;
      
      if (outcome === 'accepted') {
        toast.success('Installation started!', {
          description: 'The portfolio is being installed.',
        });
      } else {
        toast.info('Installation cancelled', {
          description: 'You can install it later from the browser menu.',
        });
      }
      
      setPwaState(prev => ({ ...prev, installPrompt: null, isInstallable: false }));
    } catch (error) {
      toast.error('Installation failed', {
        description: 'Please try again or install from browser menu.',
      });
    }
  };

  // Update PWA
  const updatePWA = () => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration().then((registration) => {
        if (registration) {
          registration.update();
          window.location.reload();
        }
      });
    }
  };

  // Share portfolio
  const sharePortfolio = async () => {
    const shareData = {
      title: 'Kenenis Dev Portfolio',
      text: 'Check out this amazing portfolio by Kenenis Dev - Full Stack Developer',
      url: window.location.origin,
    };

    try {
      if (navigator.share) {
        await navigator.share(shareData);
        toast.success('Portfolio shared!');
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(window.location.origin);
        toast.success('Portfolio link copied to clipboard!');
      }
    } catch (error) {
      // Fallback to clipboard
      try {
        await navigator.clipboard.writeText(window.location.origin);
        toast.success('Portfolio link copied to clipboard!');
      } catch (clipboardError) {
        toast.error('Sharing failed', {
          description: 'Unable to share or copy link.',
        });
      }
    }
  };

  return {
    ...pwaState,
    installPWA,
    updatePWA,
    sharePortfolio,
  };
};
