import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../services/supabase/client';
import { toast } from 'sonner';
import type { Database } from '../services/supabase/types';

type BlogPost = Database['public']['Tables']['blog_posts']['Row'];
type BlogPostInsert = Database['public']['Tables']['blog_posts']['Insert'];
type BlogPostUpdate = Database['public']['Tables']['blog_posts']['Update'];
type BlogCategory = Database['public']['Tables']['blog_categories']['Row'];
type BlogCategoryInsert = Database['public']['Tables']['blog_categories']['Insert'];
type BlogComment = Database['public']['Tables']['blog_comments']['Row'];

export interface BlogPostWithCategories extends BlogPost {
  categories?: BlogCategory[];
  author?: {
    email: string;
  };
}

// Generate slug from title
const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
};

// Calculate reading time (assuming 200 words per minute)
const calculateReadingTime = (content: string): number => {
  const wordCount = content.split(/\s+/).length;
  return Math.max(1, Math.ceil(wordCount / 200));
};

export const useBlogPosts = () => {
  const queryClient = useQueryClient();

  // Fetch all blog posts (admin view - includes drafts)
  const {
    data: allPosts,
    isLoading: isLoadingAll,
    error: errorAll,
  } = useQuery({
    queryKey: ['blog-posts-all'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('blog_posts')
        .select(`
          *,
          author:admin_users(email),
          categories:blog_post_categories(
            category:blog_categories(*)
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Transform the data to flatten categories
      return data.map(post => ({
        ...post,
        categories: post.categories?.map(pc => pc.category).filter(Boolean) || []
      })) as BlogPostWithCategories[];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch published blog posts (public view)
  const {
    data: publishedPosts,
    isLoading: isLoadingPublished,
    error: errorPublished,
  } = useQuery({
    queryKey: ['blog-posts-published'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('blog_posts')
        .select(`
          *,
          author:admin_users(email),
          categories:blog_post_categories(
            category:blog_categories(*)
          )
        `)
        .eq('status', 'published')
        .order('published_at', { ascending: false });

      if (error) throw error;

      // Transform the data to flatten categories
      return data.map(post => ({
        ...post,
        categories: post.categories?.map(pc => pc.category).filter(Boolean) || []
      })) as BlogPostWithCategories[];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Get single blog post by slug
  const getPostBySlug = async (slug: string): Promise<BlogPostWithCategories | null> => {
    const { data, error } = await supabase
      .from('blog_posts')
      .select(`
        *,
        author:admin_users(email),
        categories:blog_post_categories(
          category:blog_categories(*)
        )
      `)
      .eq('slug', slug)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }

    // Transform the data to flatten categories
    return {
      ...data,
      categories: data.categories?.map(pc => pc.category).filter(Boolean) || []
    } as BlogPostWithCategories;
  };

  // Create blog post mutation
  const createPostMutation = useMutation({
    mutationFn: async (postData: BlogPostInsert & { categoryIds?: string[] }) => {
      const { categoryIds, ...post } = postData;
      
      // Generate slug if not provided
      if (!post.slug && post.title) {
        post.slug = generateSlug(post.title);
      }

      // Calculate reading time
      if (post.content) {
        post.reading_time = calculateReadingTime(post.content);
      }

      // Set published_at if status is published
      if (post.status === 'published' && !post.published_at) {
        post.published_at = new Date().toISOString();
      }

      const { data, error } = await supabase
        .from('blog_posts')
        .insert(post)
        .select()
        .single();

      if (error) throw error;

      // Add categories if provided
      if (categoryIds && categoryIds.length > 0) {
        const categoryInserts = categoryIds.map(categoryId => ({
          post_id: data.id,
          category_id: categoryId,
        }));

        const { error: categoryError } = await supabase
          .from('blog_post_categories')
          .insert(categoryInserts);

        if (categoryError) throw categoryError;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blog-posts-all'] });
      queryClient.invalidateQueries({ queryKey: ['blog-posts-published'] });
      toast.success('Blog post created successfully!');
    },
    onError: (error) => {
      console.error('Error creating blog post:', error);
      toast.error('Failed to create blog post');
    },
  });

  // Update blog post mutation
  const updatePostMutation = useMutation({
    mutationFn: async ({ 
      id, 
      postData, 
      categoryIds 
    }: { 
      id: string; 
      postData: BlogPostUpdate; 
      categoryIds?: string[] 
    }) => {
      // Generate slug if title changed
      if (postData.title && !postData.slug) {
        postData.slug = generateSlug(postData.title);
      }

      // Calculate reading time if content changed
      if (postData.content) {
        postData.reading_time = calculateReadingTime(postData.content);
      }

      // Set published_at if status changed to published
      if (postData.status === 'published' && !postData.published_at) {
        postData.published_at = new Date().toISOString();
      }

      const { data, error } = await supabase
        .from('blog_posts')
        .update(postData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      // Update categories if provided
      if (categoryIds !== undefined) {
        // Remove existing categories
        await supabase
          .from('blog_post_categories')
          .delete()
          .eq('post_id', id);

        // Add new categories
        if (categoryIds.length > 0) {
          const categoryInserts = categoryIds.map(categoryId => ({
            post_id: id,
            category_id: categoryId,
          }));

          const { error: categoryError } = await supabase
            .from('blog_post_categories')
            .insert(categoryInserts);

          if (categoryError) throw categoryError;
        }
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blog-posts-all'] });
      queryClient.invalidateQueries({ queryKey: ['blog-posts-published'] });
      toast.success('Blog post updated successfully!');
    },
    onError: (error) => {
      console.error('Error updating blog post:', error);
      toast.error('Failed to update blog post');
    },
  });

  // Delete blog post mutation
  const deletePostMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('blog_posts')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blog-posts-all'] });
      queryClient.invalidateQueries({ queryKey: ['blog-posts-published'] });
      toast.success('Blog post deleted successfully!');
    },
    onError: (error) => {
      console.error('Error deleting blog post:', error);
      toast.error('Failed to delete blog post');
    },
  });

  // Increment view count
  const incrementViewCount = async (id: string) => {
    await supabase.rpc('increment_blog_views', { post_id: id });
  };

  return {
    // Data
    allPosts,
    publishedPosts,
    
    // Loading states
    isLoadingAll,
    isLoadingPublished,
    
    // Errors
    errorAll,
    errorPublished,
    
    // Functions
    getPostBySlug,
    incrementViewCount,
    
    // Mutations
    createPost: createPostMutation.mutate,
    updatePost: updatePostMutation.mutate,
    deletePost: deletePostMutation.mutate,
    
    // Mutation states
    isCreating: createPostMutation.isPending,
    isUpdating: updatePostMutation.isPending,
    isDeleting: deletePostMutation.isPending,
  };
};
