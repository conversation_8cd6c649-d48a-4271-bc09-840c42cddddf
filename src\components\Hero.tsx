import React, { useEffect, useRef, useState } from "react";
import { ArrowDown, Star, Sparkles } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { usePortfolioContent } from "@/hooks/usePortfolioContent";
import { supabase } from "@/integrations/supabase/client";

const Hero = () => {
  const typingTextRef = useRef<HTMLSpanElement>(null);

  // Database integration
  const { content } = usePortfolioContent();

  // Hero content state with defaults
  const [heroContent, setHeroContent] = useState({
    name: "<PERSON><PERSON><PERSON>",
    profession: "Full Stack Developer",
    welcomeText: "WELCOME TO MY WORLD!",
    description:
      "I'm a creative full stack developer specializing in building exceptional digital experiences. I build blazing-fast, scalable web apps—from pixel-perfect UIs to robust backends. Currently crafting AI-powered solutions and open-source tools to simplify workflows.",
    buttonText: "More About Me",
    profileImage:
      "https://i.postimg.cc/FH1L6nNj/1724878552606-2.jpg",
    typingTexts: ["Full Stack Developer", "UI/UX Designer", "Web Developer"],
    professions: [
      "Full Stack Developer",
      "UI/UX Designer",
      "Web Developer",
      "Software Engineer",
    ],
  });



  // Simple state for mobile name and profession
  const [mobileNameText, setMobileNameText] = useState("");
  const [showNameCursor, setShowNameCursor] = useState(true);
  const [startProfessionAnimation, setStartProfessionAnimation] =
    useState(false);
  const [currentProfession, setCurrentProfession] = useState(
    heroContent.profession
  );

  // Load hero content from database
  useEffect(() => {
    if (content && content.length > 0) {
      const heroData = content.find(item => item.section === "hero")?.content;
      if (heroData) {
        setHeroContent((prev) => ({
          ...prev,
          ...heroData,
        }));
        setCurrentProfession(heroData.profession || heroContent.profession);
      }
    }
  }, [content]);

  // Real-time subscription for hero content updates
  useEffect(() => {
    const channel = supabase
      .channel("hero_content_changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "portfolio_content",
          filter: "section=eq.hero",
        },
        (payload) => {
          if (
            payload.eventType === "UPDATE" ||
            payload.eventType === "INSERT"
          ) {
            const newContent = payload.new as any;
            if (newContent.content) {
              setHeroContent((prev) => ({
                ...prev,
                ...newContent.content,
              }));
              setCurrentProfession(
                newContent.content.profession || "Web Developer"
              );
            }
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  // Typing animation effect
  useEffect(() => {
    const texts = heroContent.typingTexts;
    if (!texts || texts.length === 0 || !typingTextRef.current) {
      return;
    }

    let timeoutId: NodeJS.Timeout;
    let currentTextIndex = 0;
    let currentCharIndex = 0;
    let isDeleting = false;

    function typeText() {
      const currentText = texts[currentTextIndex];

      if (typingTextRef.current) {
        if (isDeleting) {
          // Deleting characters
          typingTextRef.current.textContent = currentText.substring(0, currentCharIndex - 1);
          currentCharIndex--;

          if (currentCharIndex === 0) {
            isDeleting = false;
            currentTextIndex = (currentTextIndex + 1) % texts.length;
            timeoutId = setTimeout(typeText, 500); // Wait before typing new word
          } else {
            timeoutId = setTimeout(typeText, 50); // Faster when deleting
          }
        } else {
          // Typing characters
          typingTextRef.current.textContent = currentText.substring(0, currentCharIndex + 1);
          currentCharIndex++;

          if (currentCharIndex === currentText.length) {
            isDeleting = true;
            timeoutId = setTimeout(typeText, 2000); // Wait before deleting
          } else {
            timeoutId = setTimeout(typeText, 100); // Normal typing speed
          }
        }
      }
    }

    // Start the animation after a short delay
    timeoutId = setTimeout(typeText, 500);

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [heroContent.typingTexts]);

  // Enhanced mobile name typing animation with cursor control
  useEffect(() => {
    const name = heroContent.name;
    let nameIndex = 0;

    const typeNameChar = () => {
      if (nameIndex <= name.length) {
        setMobileNameText(name.substring(0, nameIndex));
        nameIndex++;
        setTimeout(typeNameChar, 150);
      } else {
        // Name typing complete - hide cursor and start profession animation
        setTimeout(() => {
          setShowNameCursor(false);
          setTimeout(() => {
            setStartProfessionAnimation(true);
          }, 300);
        }, 500);
      }
    };

    // Reset animation when name changes
    setMobileNameText("");
    setShowNameCursor(true);
    setStartProfessionAnimation(false);

    typeNameChar();
  }, [heroContent.name]);

  // Cycle through professions after animation starts
  useEffect(() => {
    if (startProfessionAnimation) {
      let professionIndex = 0;

      const cycleProfessions = () => {
        setCurrentProfession(heroContent.professions[professionIndex]);
        professionIndex =
          (professionIndex + 1) % heroContent.professions.length;
      };

      // Start immediately with first profession
      cycleProfessions();

      // Then cycle every 4 seconds
      const interval = setInterval(cycleProfessions, 4000);

      return () => clearInterval(interval);
    }
  }, [startProfessionAnimation, heroContent.professions]);

  return (
    <section
      id="home"
      className="min-h-screen flex items-center justify-center py-20 lg:pt-[100px] lg:pb-0"
    >
      <div className="container mx-auto px-4 lg:px-8">
        <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8 lg:gap-12">
          {/* Mobile Profile Section - Only visible on mobile */}
          <div className="lg:hidden flex-shrink-0 text-center hero-profile-image">
            {/* Enhanced Profile Image with Rotating Border */}
            <div className="relative mx-auto w-36 h-36 mb-5 mt-20 group">
              {/* Outer Rotating Gradient Ring */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-red via-white to-red p-0.5 animate-spin-slower">
                <div className="w-full h-full rounded-full bg-dark-bg"></div>
              </div>

              {/* Main Image Container */}
              <div className="absolute inset-0.5 rounded-full overflow-hidden ring-2 ring-red/30 group-hover:ring-red/50 transition-all duration-500">
                <Avatar className="w-full h-full shadow-xl">
                  <AvatarImage
                    src={heroContent.profileImage}
                    alt={heroContent.name}
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <AvatarFallback className="bg-red/20 text-red text-3xl font-bold">
                    {heroContent.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>

                {/* Image Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-red/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Online status indicator */}
                <div className="absolute bottom-1 right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-dark-bg animate-pulse shadow-md z-10"></div>
              </div>

              {/* Floating Stars */}
              <Star className="absolute -top-2 -right-2 w-4 h-4 text-yellow-400 animate-pulse" />
              <Sparkles className="absolute -bottom-1 -left-2 w-3 h-3 text-purple-400 animate-pulse delay-1000" />
            </div>

            {/* Name and Profession with Enhanced Cursors */}
            <div className="space-y-2 mb-3">
              {/* Name with enhanced colored cursor */}
              <h1 className="text-xl md:text-2xl font-bold mobile-name-text leading-tight">
                {mobileNameText}
                {showNameCursor && (
                  <span className="enhanced-name-cursor"></span>
                )}
              </h1>

              {/* Profession with smooth CSS animation - starts after name */}
              <div className="h-6 flex items-center justify-center mb-1">
                {startProfessionAnimation && (
                  <div
                    key={currentProfession}
                    className="typing-text text-base md:text-lg font-medium mobile-profession-text"
                  >
                    <span>{currentProfession}</span>
                  </div>
                )}
              </div>

              {/* Decorative line */}
              <div className="w-14 h-0.5 bg-gradient-to-r from-red to-red/50 mx-auto mt-1"></div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 space-y-8 text-center lg:text-left">
            {/* Enhanced Welcome Text */}
            <div className="relative">
              <h3 className="welcome-text text-lg md:text-xl font-semibold tracking-[0.3em] mb-2">
                {heroContent.welcomeText}
              </h3>
              <div className="welcome-underline"></div>
            </div>
            {/* Enhanced Main Heading */}
            <div className="relative">
              <h1 className="enhanced-heading text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                <span className="hero-greeting">Hi, I'm </span>
                <span className="hero-name">{heroContent.name}</span>
                <br />
                <span className="hero-article">a </span>
                <span
                  ref={typingTextRef}
                  className="hero-profession"
                >
                  Web Developer
                </span>
                <span className="typing-cursor-simple">|</span>
              </h1>
              {/* Decorative elements */}
              <div className="hero-glow-effect"></div>
              <div className="hero-particles"></div>
            </div>
            {/* Enhanced Description */}
            <div className="relative">
              <p className="hero-description text-lg md:text-xl text-gray-200 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                {heroContent.description}
              </p>
            </div>

            {/* Enhanced Button */}
            <div className="pt-6">
              <a href="#about" className="enhanced-btn group">
                <span className="btn-text">{heroContent.buttonText}</span>
                <div className="btn-glow"></div>
                <div className="btn-particles"></div>
              </a>
            </div>
          </div>
        </div>
      </div>

      <div className="absolute bottom-10 left-0 w-full flex justify-center animate-bounce-slow">
        <a href="#about" className="text-gray-300 hover:text-red">
          <ArrowDown size={30} />
        </a>
      </div>
    </section>
  );
};

export default Hero;
