import React, { useState, useRef, useEffect } from "react";
import { usePortfolioItems, usePortfolioContent } from "../../hooks/usePortfolioContent";
import { usePortfolioCategories } from "../../hooks/usePortfolioCategories";
import { useFallbackDataSettings } from "../../hooks/useSiteSettings";
import { supabase } from "../../services/supabase/client";

const Portfolio = () => {
  const [activeCategory, setActiveCategory] = useState("All");
  const [isAnimating, setIsAnimating] = useState(false);
  const portfolioRef = useRef<HTMLElement>(null);

  // Database integration for portfolio content
  const { content } = usePortfolioContent();
  const { enableFallbackData } = useFallbackDataSettings();

  // Portfolio content state with defaults
  const [portfolioContent, setPortfolioContent] = useState({
    title: "My Portfolio",
    description: "Discover my latest creative works and innovative solutions across various digital platforms and technologies.",
    highlightedWords: [],
  });

  // Function to render text with highlighted words
  const renderTextWithHighlights = (text, highlightedWords = []) => {
    if (!highlightedWords || highlightedWords.length === 0) {
      return text;
    }

    let highlightedText = text;
    highlightedWords.forEach((word) => {
      const regex = new RegExp(`\\b(${word})\\b`, 'gi');
      highlightedText = highlightedText.replace(
        regex,
        '<span class="highlight-text">$1</span>'
      );
    });

    return <span dangerouslySetInnerHTML={{ __html: highlightedText }} />;
  };

  // Get portfolio items and categories from database
  const {
    portfolioItems: dbPortfolioItems,
    isLoading,
    error,
    getCategories,
    getItemsByCategory
  } = usePortfolioItems();

  const {
    categories: dbCategories,
    isLoading: categoriesLoading,
  } = usePortfolioCategories();

  // Load portfolio content from database
  useEffect(() => {
    if (content && content.length > 0) {
      const portfolioData = content.find(item => item.section === "portfolio")?.content;
      if (portfolioData) {
        setPortfolioContent(prev => ({
          ...prev,
          title: portfolioData.title || prev.title,
          description: portfolioData.description || prev.description,
          highlightedWords: portfolioData.highlightedWords || prev.highlightedWords,
        }));
      }
    }
  }, [content]);

  // Real-time subscription for portfolio content updates
  useEffect(() => {
    const channel = supabase
      .channel("portfolio_content_changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "portfolio_content",
          filter: "section=eq.portfolio",
        },
        (payload) => {
          if (
            payload.eventType === "UPDATE" ||
            payload.eventType === "INSERT"
          ) {
            const newContent = payload.new as any;
            if (newContent.content) {
              setPortfolioContent(prev => ({
                ...prev,
                title: newContent.content.title || prev.title,
                description: newContent.content.description || prev.description,
                highlightedWords: newContent.content.highlightedWords || prev.highlightedWords,
              }));
            }
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  // Fallback portfolio items for when database is empty or loading
  const fallbackPortfolioItems = [
    {
      id: "fallback-1",
      title: "Campus & School Attendance Tracker App",
      category: "Real World App",
      description: "AI powered secure attendance tracking app",
      technologies: ["React js", "TypeScript", "Node.js", "PostgreSQL"],
      image_url:
        "https://i.ibb.co/S4gX2cZB/20250712-021640-0000.png",
      live_url: null,
      github_url: null,
      featured: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: "fallback-2",
      title: "Amazon clone",
      category: "Web dev",
      description: "Modern Amazon clone with React, Node.js, & Firebase auth",
      technologies: ["React.js", "Node.js", "Firebase", "MySQL"],
      image_url:
        "https://www.webdevelopmentindia.biz/wp-content/uploads/2024/09/amazon-clone-development-amazon-clone-budget.webp",
      live_url: null,
      github_url: null,
      featured: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: "fallback-3",
      title: "Netflix clone",
      category: "Web dev",
      description: "Netflix clone with react.js and Firebase auth.",
      technologies: ["HTML", "CSS", "React", "Firebase"],
      image_url:
        "https://img-s3.onedio.com/id-684133b82b739bb8d69abda2/rev-0/w-600/h-337/f-jpg/s-093c8f79946ff192d7575b1cfc61bac079ce8fbe.jpg",
      live_url: null,
      github_url: null,
      featured: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: "fallback-4",
      title: "Apple.com clone",
      category: "Web dev",
      description: "Latest Apple.com clone with react.js and Bootstrap.",
      technologies: ["HTML", "CSS", "JavaScript", "Bootstrap"],
      image_url:
        "https://miro.medium.com/v2/resize:fit:1400/1*upQ06nwYQVsTVY4sX2B_rA.png",
      live_url: null,
      github_url: null,
      featured: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: "fallback-5",
      title: "Portfolio website",
      category: "Web Design",
      description: "Portfolio website using HTML, CSS, and JavaScript with modern design principles.",
      technologies: ["Html", "CSS", "javaScript", "Framer Motion"],
      image_url:
        "https://mir-s3-cdn-cf.behance.net/projects/404/1a19e1184177031.Y3JvcCw0NjAyLDM2MDAsMTAyLDA.jpg",
      live_url: null,
      github_url: null,
      featured: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: "fallback-6",
      title: "Full stack portfolio",
      category: "UI/UX Design",
      description: "Full stack portfolio website with SQL database created with modern design principles.",
      technologies: ["HTML", "CSS", "JavaScript", "Bootstrap"],
      image_url:
        "https://i.ibb.co/fYw9mwFF/Screenshot-2025-07-03-234245.png",
      live_url: null,
      github_url: null,
      featured: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
  ];

  // Use database items if available, otherwise use fallback (if enabled)
  const portfolioItems = dbPortfolioItems && dbPortfolioItems.length > 0
    ? dbPortfolioItems
    : (enableFallbackData ? fallbackPortfolioItems : []);

  // Get categories dynamically from database or use fallback (if enabled)
  const categories = dbCategories && dbCategories.length > 0
    ? dbCategories.map(cat => cat.name)
    : (enableFallbackData ? ["All", "Web Design", "Mobile App", "Dashboard", "Landing Page"] : ["All"]);

  // Filter portfolio items based on active category
  const filteredItems = dbPortfolioItems && dbPortfolioItems.length > 0
    ? getItemsByCategory(activeCategory)
    : activeCategory === "All"
      ? portfolioItems
      : portfolioItems.filter((item) => item.category === activeCategory);

  // Loading state
  if (isLoading) {
    return (
      <section
        id="portfolio"
        ref={portfolioRef}
        className="section bg-dark-darker relative overflow-hidden"
      >
        <div className="container mx-auto relative z-10">
          <div className="text-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red mx-auto mb-4"></div>
            <p className="text-gray-400">Loading portfolio items...</p>
          </div>
        </div>
      </section>
    );
  }

  // Error state
  if (error) {
    console.error("Portfolio error:", error);
    // Continue with fallback data instead of showing error
  }

  // Handle category change with animation
  const handleCategoryChange = (category: string) => {
    if (category === activeCategory) return;

    setIsAnimating(true);
    setTimeout(() => {
      setActiveCategory(category);
      setIsAnimating(false);
    }, 300);
  };

  return (
    <section
      id="portfolio"
      ref={portfolioRef}
      className="section bg-dark-darker relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="portfolio-bg-glow"></div>
      <div className="portfolio-particles"></div>
      <div className="portfolio-grid-pattern"></div>

      <div className="container mx-auto relative z-10">
        {/* Enhanced Title Section */}
        <div className="text-center mb-16">
          <div className="relative inline-block">
            <h2 className="enhanced-portfolio-title">{portfolioContent.title}</h2>
            <div className="portfolio-title-glow"></div>
            <div className="portfolio-title-underline"></div>
          </div>

          <div className="portfolio-description-container">
            <p className="portfolio-description">
              {renderTextWithHighlights(portfolioContent.description, portfolioContent.highlightedWords)}
            </p>
          </div>
        </div>

        {/* Enhanced Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-16 px-4">
          {categories.map((category, index) => (
            <button
              key={index}
              className={`enhanced-filter-btn ${
                activeCategory === category ? "active" : ""
              }`}
              onClick={() => handleCategoryChange(category)}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <span className="filter-btn-text">{category}</span>
              <div className="filter-btn-glow"></div>
              <div className="filter-btn-particles"></div>
            </button>
          ))}
        </div>

        {/* Enhanced Portfolio Grid */}
        <div className={`portfolio-grid ${isAnimating ? "animating" : ""}`}>
          {filteredItems.map((item, index) => (
            <div
              key={item.id}
              className="enhanced-portfolio-item"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="portfolio-card">
                <div className="portfolio-image-container">
                  <img
                    src={item.image_url || item.image || "/api/placeholder/600/400"}
                    alt={item.title}
                    className="portfolio-image"
                    loading="lazy"
                  />
                  <div className="portfolio-overlay">
                    <div className="portfolio-content">
                      <div className="portfolio-year">
                        {new Date(item.created_at).getFullYear()}
                      </div>
                      <h4 className="portfolio-title">{item.title}</h4>
                      <p className="portfolio-description">
                        {item.description}
                      </p>
                      <div className="portfolio-technologies">
                        {item.technologies.map((tech, techIndex) => (
                          <span key={techIndex} className="tech-badge">
                            {tech}
                          </span>
                        ))}
                      </div>
                      <div className="portfolio-category">{item.category}</div>

                      {/* Action buttons for live demo and source code */}
                      {(item.live_url || item.github_url) && (
                        <div className="portfolio-actions mt-4 flex gap-2">
                          {item.live_url && (
                            <a
                              href={item.live_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="portfolio-action-btn"
                            >
                              Live Demo
                            </a>
                          )}
                          {item.github_url && (
                            <a
                              href={item.github_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="portfolio-action-btn"
                            >
                              Source Code
                            </a>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="portfolio-glow"></div>
                  <div className="portfolio-particles"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
