import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "../services/supabase/client";
import { toast } from "sonner";

export interface SiteSetting {
  id: string;
  key: string;
  value: any;
  created_at: string;
  updated_at: string;
}

export const useSiteSettings = () => {
  const queryClient = useQueryClient();

  // Fetch all settings
  const {
    data: settings,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["site-settings"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("site_settings")
        .select("*")
        .order("key");

      if (error) throw error;
      return data as SiteSetting[];
    },
  });

  // Get setting by key
  const getSettingByKey = (key: string) => {
    return settings?.find((setting) => setting.key === key)?.value;
  };

  // Update setting mutation
  const updateSettingMutation = useMutation({
    mutationFn: async ({ key, value }: { key: string; value: any }) => {
      const { data, error } = await supabase
        .from("site_settings")
        .upsert(
          {
            key,
            value,
            updated_at: new Date().toISOString(),
          },
          {
            onConflict: "key", // Specify the conflict column
            ignoreDuplicates: false, // We want to update, not ignore
          }
        )
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["site-settings"] });
      toast.success("Settings updated successfully!");
    },
    onError: (error) => {
      console.error("Error updating settings:", error);
      toast.error("Failed to update settings");
    },
  });

  // Delete setting mutation
  const deleteSettingMutation = useMutation({
    mutationFn: async (key: string) => {
      const { error } = await supabase
        .from("site_settings")
        .delete()
        .eq("key", key);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["site-settings"] });
      toast.success("Setting deleted successfully!");
    },
    onError: (error) => {
      console.error("Error deleting setting:", error);
      toast.error("Failed to delete setting");
    },
  });

  // Bulk update settings
  const bulkUpdateMutation = useMutation({
    mutationFn: async (settingsToUpdate: { key: string; value: any }[]) => {
      const updates = settingsToUpdate.map((setting) => ({
        key: setting.key,
        value: setting.value,
        updated_at: new Date().toISOString(),
      }));

      const { data, error } = await supabase
        .from("site_settings")
        .upsert(updates, {
          onConflict: "key", // Specify the conflict column
          ignoreDuplicates: false, // We want to update, not ignore
        })
        .select();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["site-settings"] });
      toast.success("Settings updated successfully!");
    },
    onError: (error) => {
      console.error("Error updating settings:", error);
      toast.error("Failed to update settings");
    },
  });

  return {
    settings,
    isLoading,
    error,
    getSettingByKey,
    updateSetting: updateSettingMutation.mutate,
    deleteSetting: deleteSettingMutation.mutate,
    bulkUpdateSettings: bulkUpdateMutation.mutate,
    isUpdating: updateSettingMutation.isPending || bulkUpdateMutation.isPending,
    isDeleting: deleteSettingMutation.isPending,
  };
};

// Helper hook for specific setting categories
export const useGeneralSettings = () => {
  const { getSettingByKey, updateSetting, isUpdating } = useSiteSettings();

  const generalSettings = getSettingByKey("general") || {
    siteName: "Kenenis Dev Portfolio",
    siteDescription: "Personal Portfolio & Resume",
    tagline: "Personal Portfolio",
    contactEmail: "<EMAIL>",
    phone: "****** 567 8900",
    address: "123 Main St, City, Country",
    language: "en",
    maintenanceMode: false,
    enableFallbackData: true, // New setting to control fallback data behavior
  };

  const updateGeneralSettings = (newSettings: any) => {
    updateSetting({ key: "general", value: newSettings });
  };

  return {
    generalSettings,
    updateGeneralSettings,
    isUpdating,
  };
};



export const useSocialSettings = () => {
  const { getSettingByKey, updateSetting, isUpdating } = useSiteSettings();

  const socialSettings = getSettingByKey("social") || {
    linkedin: "",
    github: "",
    twitter: "",
    instagram: "",
    facebook: "",
    youtube: "",
  };

  const updateSocialSettings = (newSettings: any) => {
    updateSetting({ key: "social", value: newSettings });
  };

  return {
    socialSettings,
    updateSocialSettings,
    isUpdating,
  };
};



export const useCVSettings = () => {
  const { getSettingByKey, updateSetting, isUpdating } = useSiteSettings();

  const cvSettings = getSettingByKey("cv") || {
    cvFileUrl: "",
    cvFileName: "CV.pdf",
    enableDownload: true,
  };

  const updateCVSettings = (newSettings: any) => {
    updateSetting({ key: "cv", value: newSettings });
  };

  return {
    cvSettings,
    updateCVSettings,
    isUpdating,
  };
};

// Hook specifically for fallback data control
export const useFallbackDataSettings = () => {
  const { getSettingByKey } = useSiteSettings();

  const generalSettings = getSettingByKey("general") || {};

  // Default to true if not set (current behavior)
  const enableFallbackData = generalSettings.enableFallbackData !== undefined
    ? generalSettings.enableFallbackData
    : true;

  return {
    enableFallbackData,
  };
};
