-- Update testimonial policies for admin access
-- Run this in Supabase SQL Editor

-- Add policy for admins to view all testimonials (not just approved ones)
CREATE POLICY "Admins can view all testimonials" ON testimonials
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM admin_users au 
            WHERE au.id = auth.uid() AND au.is_active = true
        )
    );

-- Verify policies are created
SELECT schemaname, tablename, policyname, cmd, qual 
FROM pg_policies 
WHERE tablename = 'testimonials';
