import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import { Shield, Loader2 } from "lucide-react";

interface AdminAuthGuardProps {
  children: React.ReactNode;
}

const AdminAuthGuard: React.FC<AdminAuthGuardProps> = ({ children }) => {
  const { user, loading, isAdmin } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        // Not logged in - redirect to admin login
        navigate("/admin");
        return;
      }

      if (!isAdmin) {
        // Give a short delay for admin check to complete
        const timer = setTimeout(() => {
          if (!isAdmin) {
            // Logged in but not admin - redirect to admin login
            navigate("/admin");
          }
        }, 1000);

        return () => clearTimeout(timer);
      }
    }
  }, [user, loading, isAdmin, navigate]);

  // Show loading while checking authentication (but only if we don't have cached admin status)
  if (loading && !user) {
    return (
      <div className="min-h-screen bg-dark-bg flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-red to-red/70 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <div className="flex items-center justify-center gap-2 text-white">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span>Verifying admin access...</span>
          </div>
        </div>
      </div>
    );
  }

  // Show loading if not authenticated yet (but only briefly)
  if (!user || (!isAdmin && loading)) {
    return (
      <div className="min-h-screen bg-dark-bg flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-red to-red/70 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <div className="text-white">
            <span>Redirecting to login...</span>
          </div>
        </div>
      </div>
    );
  }

  // User is authenticated and is admin - render the protected content
  return <>{children}</>;
};

export default AdminAuthGuard;
