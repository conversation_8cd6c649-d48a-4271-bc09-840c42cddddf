import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Language, DEFAULT_LANGUAGE, getBrowserLanguage, useTranslation } from '@/utils/language';
import { useSiteSettings } from '@/hooks/useSiteSettings';

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: keyof typeof import('@/utils/language').translations.en) => string;
  isRTL: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>(DEFAULT_LANGUAGE);
  const { getSettingByKey } = useSiteSettings();
  const { t, isRTL } = useTranslation(language);

  // Load language from settings or browser preference
  useEffect(() => {
    const savedLanguage = getSettingByKey('general')?.language;
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'tr')) {
      setLanguageState(savedLanguage);
    } else {
      // Fallback to browser language or default
      const browserLang = getBrowserLanguage();
      setLanguageState(browserLang);
    }
  }, [getSettingByKey]);

  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage);
    // Note: The actual saving to database should be handled by the settings component
    // This just updates the local state for immediate UI changes
  };

  const value: LanguageContextType = {
    language,
    setLanguage,
    t,
    isRTL,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Hook to get just the translation function
export const useT = () => {
  const { t } = useLanguage();
  return t;
};
