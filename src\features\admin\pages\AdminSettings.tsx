import React, { useState, useEffect, useRef } from "react";
import AdminLayout from "../components/AdminLayout";
import {
  Settings,
  Save,
  Bell,
  Upload,
  Download,
  FileText,
  RefreshCw,
  Linkedin,
  Github,
  Twitter,
  Instagram,
  Facebook,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import {
  useGeneralSettings,
  useSocialSettings,
  useCVSettings,
} from "../../../hooks/useSiteSettings";
import { SUPPORTED_LANGUAGES } from "../../../utils/language";
import FallbackDataDemo from "../../../components/admin/FallbackDataDemo";

const AdminSettings = () => {
  const [activeTab, setActiveTab] = useState("general");

  // Refs to track initialization
  const initializedGeneral = useRef(false);
  const initializedSocial = useRef(false);
  const initializedCV = useRef(false);

  // Database hooks
  const {
    generalSettings,
    updateGeneralSettings,
    isUpdating: isUpdatingGeneral,
  } = useGeneralSettings();



  const {
    socialSettings,
    updateSocialSettings,
    isUpdating: isUpdatingSocial,
  } = useSocialSettings();



  const {
    cvSettings,
    updateCVSettings,
    isUpdating: isUpdatingCV,
  } = useCVSettings();

  const settingsTabs = [
    { id: "general", label: "General", icon: Settings },
    { id: "social", label: "Social Media", icon: Bell },
    { id: "cv", label: "CV Download", icon: FileText },
  ];

  // Local state for editing with safe defaults
  const [localGeneralSettings, setLocalGeneralSettings] = useState({
    siteName: "Kenenis Dev Portfolio",
    siteDescription: "Personal Portfolio & Resume",
    contactEmail: "<EMAIL>",
    language: "en",
    maintenanceMode: false,
    enableFallbackData: true,
  });



  const [localSocialSettings, setLocalSocialSettings] = useState({
    linkedin: "",
    github: "",
    twitter: "",
    instagram: "",
    facebook: "",
  });



  const [localCVSettings, setLocalCVSettings] = useState({
    cvFileUrl: "",
    cvFileName: "CV.pdf",
    enableDownload: true,
  });

  // Local notification settings (not connected to database yet)
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    newMessages: true,
    systemAlerts: true,
    weeklyReports: false,
    instantAlerts: true,
  });

  // Update local state when database data changes (only once per setting type)
  useEffect(() => {
    if (generalSettings && Object.keys(generalSettings).length > 0 && !initializedGeneral.current) {
      setLocalGeneralSettings((prev) => ({ ...prev, ...generalSettings }));
      initializedGeneral.current = true;
    }
  }, [generalSettings]);

  useEffect(() => {
    if (socialSettings && Object.keys(socialSettings).length > 0 && !initializedSocial.current) {
      setLocalSocialSettings((prev) => ({ ...prev, ...socialSettings }));
      initializedSocial.current = true;
    }
  }, [socialSettings]);



  useEffect(() => {
    if (cvSettings && Object.keys(cvSettings).length > 0 && !initializedCV.current) {
      setLocalCVSettings((prev) => ({ ...prev, ...cvSettings }));
      initializedCV.current = true;
    }
  }, [cvSettings]);

  const handleSave = async () => {
    try {
      // Save settings based on active tab
      switch (activeTab) {
        case "general":
          updateGeneralSettings(localGeneralSettings);
          break;

        case "social":
          updateSocialSettings(localSocialSettings);
          break;

        case "cv":
          updateCVSettings(localCVSettings);
          break;
        default:
          toast.info(`${activeTab} settings will be implemented soon`);
          return;
      }
    } catch (error) {
      console.error("Error saving settings:", error);
      toast.error("Failed to save settings");
    }
  };

  const isLoading =
    isUpdatingGeneral ||
    isUpdatingSocial;

  const renderGeneralSettings = () => (
    <div className="space-y-4 sm:space-y-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
        <div className="space-y-2">
          <Label htmlFor="siteName" className="text-white text-sm sm:text-base">
            Site Name
          </Label>
          <Input
            id="siteName"
            value={localGeneralSettings.siteName}
            onChange={(e) =>
              setLocalGeneralSettings((prev) => ({
                ...prev,
                siteName: e.target.value,
              }))
            }
            className="bg-dark-bg border-red/30 text-white text-sm break-all"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="contactEmail" className="text-white text-sm sm:text-base">
            Contact Email
          </Label>
          <Input
            id="contactEmail"
            type="email"
            value={localGeneralSettings.contactEmail}
            onChange={(e) =>
              setLocalGeneralSettings((prev) => ({
                ...prev,
                contactEmail: e.target.value,
              }))
            }
            className="bg-dark-bg border-red/30 text-white text-sm break-all"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="siteDescription" className="text-white text-sm sm:text-base">
          Site Description
        </Label>
        <Textarea
          id="siteDescription"
          value={localGeneralSettings.siteDescription}
          onChange={(e) =>
            setLocalGeneralSettings((prev) => ({
              ...prev,
              siteDescription: e.target.value,
            }))
          }
          className="bg-dark-bg border-red/30 text-white text-sm"
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="language" className="text-white text-sm sm:text-base">
          Language
        </Label>
        <select
          id="language"
          value={localGeneralSettings.language}
          onChange={(e) =>
            setLocalGeneralSettings((prev) => ({
              ...prev,
              language: e.target.value,
            }))
          }
          className="w-full px-2 sm:px-3 py-2 bg-dark-bg border border-red/30 rounded-lg text-white text-sm"
        >
          {SUPPORTED_LANGUAGES.map((lang) => (
            <option key={lang.code} value={lang.code}>
              {lang.nativeName}
            </option>
          ))}
        </select>
      </div>

      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4 p-3 sm:p-4 bg-dark-bg/50 rounded-lg border border-red/20">
        <div className="flex items-center gap-3">
          <div>
            <p className="text-white font-medium text-sm sm:text-base">Maintenance Mode</p>
            <p className="text-gray-400 text-xs sm:text-sm">
              Temporarily disable public access
            </p>
          </div>
        </div>
        <Switch
          checked={localGeneralSettings.maintenanceMode}
          onCheckedChange={(checked) =>
            setLocalGeneralSettings((prev) => ({
              ...prev,
              maintenanceMode: checked,
            }))
          }
        />
      </div>

      {/* Fallback Data Control */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4 p-3 sm:p-4 bg-dark-bg/50 rounded-lg border border-red/20">
        <div className="flex items-center gap-3">
          <div>
            <p className="text-white font-medium text-sm sm:text-base">Enable Fallback Data</p>
            <p className="text-gray-400 text-xs sm:text-sm">
              Show hardcoded content while database data is loading or unavailable
            </p>
          </div>
        </div>
        <Switch
          checked={localGeneralSettings.enableFallbackData}
          onCheckedChange={(checked) =>
            setLocalGeneralSettings((prev) => ({
              ...prev,
              enableFallbackData: checked,
            }))
          }
        />
      </div>

      {/* Fallback Data Status Demo */}
      <FallbackDataDemo />
    </div>
  );



  const renderNotificationSettings = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between p-4 bg-dark-bg/50 rounded-lg border border-red/20">
        <div>
          <p className="text-white font-medium">Email Notifications</p>
          <p className="text-gray-400 text-sm">
            Receive notifications via email
          </p>
        </div>
        <Switch
          checked={notificationSettings.emailNotifications}
          onCheckedChange={(checked) =>
            setNotificationSettings((prev) => ({
              ...prev,
              emailNotifications: checked,
            }))
          }
        />
      </div>

      <div className="flex items-center justify-between p-4 bg-dark-bg/50 rounded-lg border border-red/20">
        <div>
          <p className="text-white font-medium">New Messages</p>
          <p className="text-gray-400 text-sm">
            Get notified of new contact messages
          </p>
        </div>
        <Switch
          checked={notificationSettings.newMessages}
          onCheckedChange={(checked) =>
            setNotificationSettings((prev) => ({
              ...prev,
              newMessages: checked,
            }))
          }
        />
      </div>

      <div className="flex items-center justify-between p-4 bg-dark-bg/50 rounded-lg border border-red/20">
        <div>
          <p className="text-white font-medium">System Alerts</p>
          <p className="text-gray-400 text-sm">
            Important system notifications
          </p>
        </div>
        <Switch
          checked={notificationSettings.systemAlerts}
          onCheckedChange={(checked) =>
            setNotificationSettings((prev) => ({
              ...prev,
              systemAlerts: checked,
            }))
          }
        />
      </div>

      <div className="flex items-center justify-between p-4 bg-dark-bg/50 rounded-lg border border-red/20">
        <div>
          <p className="text-white font-medium">Weekly Reports</p>
          <p className="text-gray-400 text-sm">
            Receive weekly analytics reports
          </p>
        </div>
        <Switch
          checked={notificationSettings.weeklyReports}
          onCheckedChange={(checked) =>
            setNotificationSettings((prev) => ({
              ...prev,
              weeklyReports: checked,
            }))
          }
        />
      </div>

      <div className="flex items-center justify-between p-4 bg-dark-bg/50 rounded-lg border border-red/20">
        <div>
          <p className="text-white font-medium">Instant Alerts</p>
          <p className="text-gray-400 text-sm">
            Real-time browser notifications
          </p>
        </div>
        <Switch
          checked={notificationSettings.instantAlerts}
          onCheckedChange={(checked) =>
            setNotificationSettings((prev) => ({
              ...prev,
              instantAlerts: checked,
            }))
          }
        />
      </div>
    </div>
  );

  const renderSocialSettings = () => (
    <div className="space-y-4 sm:space-y-6">
      <div className="space-y-3 sm:space-y-4">
        <Label htmlFor="linkedin" className="text-white flex items-center gap-2 text-sm sm:text-base">
          <Linkedin className="w-3 h-3 sm:w-4 sm:h-4 text-blue-400" />
          LinkedIn Profile URL
        </Label>
        <Input
          id="linkedin"
          type="url"
          placeholder="https://linkedin.com/in/yourprofile"
          value={localSocialSettings.linkedin}
          onChange={(e) =>
            setLocalSocialSettings((prev) => ({
              ...prev,
              linkedin: e.target.value,
            }))
          }
          className="bg-dark-bg border-red/30 text-white text-sm break-all"
        />
      </div>

      <div className="space-y-3 sm:space-y-4">
        <Label htmlFor="github" className="text-white flex items-center gap-2 text-sm sm:text-base">
          <Github className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400" />
          GitHub Profile URL
        </Label>
        <Input
          id="github"
          type="url"
          placeholder="https://github.com/yourusername"
          value={localSocialSettings.github}
          onChange={(e) =>
            setLocalSocialSettings((prev) => ({
              ...prev,
              github: e.target.value,
            }))
          }
          className="bg-dark-bg border-red/30 text-white text-sm break-all"
        />
      </div>

      <div className="space-y-3 sm:space-y-4">
        <Label htmlFor="twitter" className="text-white flex items-center gap-2 text-sm sm:text-base">
          <Twitter className="w-3 h-3 sm:w-4 sm:h-4 text-blue-400" />
          Twitter/X Profile URL
        </Label>
        <Input
          id="twitter"
          type="url"
          placeholder="https://twitter.com/yourusername"
          value={localSocialSettings.twitter}
          onChange={(e) =>
            setLocalSocialSettings((prev) => ({
              ...prev,
              twitter: e.target.value,
            }))
          }
          className="bg-dark-bg border-red/30 text-white text-sm break-all"
        />
      </div>

      <div className="space-y-4">
        <Label htmlFor="instagram" className="text-white flex items-center gap-2">
          <Instagram className="w-4 h-4 text-pink-400" />
          Instagram Profile URL
        </Label>
        <Input
          id="instagram"
          type="url"
          placeholder="https://instagram.com/yourusername"
          value={localSocialSettings.instagram}
          onChange={(e) =>
            setLocalSocialSettings((prev) => ({
              ...prev,
              instagram: e.target.value,
            }))
          }
          className="bg-dark-bg border-red/30 text-white"
        />
      </div>

      <div className="space-y-4">
        <Label htmlFor="facebook" className="text-white flex items-center gap-2">
          <Facebook className="w-4 h-4 text-blue-500" />
          Facebook Profile URL
        </Label>
        <Input
          id="facebook"
          type="url"
          placeholder="https://facebook.com/yourusername"
          value={localSocialSettings.facebook}
          onChange={(e) =>
            setLocalSocialSettings((prev) => ({
              ...prev,
              facebook: e.target.value,
            }))
          }
          className="bg-dark-bg border-red/30 text-white"
        />
      </div>
    </div>
  );



  const renderCVSettings = () => (
    <div className="space-y-4 sm:space-y-6">
      <div className="bg-dark-bg/50 rounded-lg border border-red/20 p-3 sm:p-6">
        <h3 className="text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4 flex items-center gap-2">
          <FileText className="w-4 h-4 sm:w-5 sm:h-5 text-red" />
          CV Download Configuration
        </h3>

        <div className="space-y-3 sm:space-y-4">
          {/* Enable Download Toggle */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4 p-3 sm:p-4 bg-dark-bg/30 rounded-lg border border-red/10">
            <div>
              <p className="text-white font-medium text-sm sm:text-base">Enable CV Download</p>
              <p className="text-gray-400 text-xs sm:text-sm">
                Allow visitors to download your CV
              </p>
            </div>
            <Switch
              checked={localCVSettings.enableDownload}
              onCheckedChange={(checked) =>
                setLocalCVSettings((prev) => ({
                  ...prev,
                  enableDownload: checked,
                }))
              }
            />
          </div>

          {/* CV File URL */}
          <div className="space-y-2">
            <Label htmlFor="cvFileUrl" className="text-white text-sm sm:text-base">
              CV File URL
            </Label>
            <Input
              id="cvFileUrl"
              type="url"
              placeholder="https://example.com/path/to/your-cv.pdf"
              value={localCVSettings.cvFileUrl}
              onChange={(e) =>
                setLocalCVSettings((prev) => ({
                  ...prev,
                  cvFileUrl: e.target.value,
                }))
              }
              className="bg-dark-bg border-red/30 text-white text-sm break-all"
            />
            <p className="text-gray-400 text-xs sm:text-sm">
              Upload your CV to a cloud service (Google Drive, Dropbox, etc.) and paste the direct download link here
            </p>
          </div>

          {/* CV File Name */}
          <div className="space-y-2">
            <Label htmlFor="cvFileName" className="text-white">
              CV File Name
            </Label>
            <Input
              id="cvFileName"
              type="text"
              placeholder="Kenenis_Dev_CV.pdf"
              value={localCVSettings.cvFileName}
              onChange={(e) =>
                setLocalCVSettings((prev) => ({
                  ...prev,
                  cvFileName: e.target.value,
                }))
              }
              className="bg-dark-bg border-red/30 text-white"
            />
            <p className="text-gray-400 text-sm">
              This will be the filename when users download your CV
            </p>
          </div>

          {/* Upload Instructions */}
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
            <h4 className="text-blue-400 font-medium mb-2 flex items-center gap-2">
              <Upload className="w-4 h-4" />
              How to Upload Your CV
            </h4>
            <ol className="text-gray-300 text-sm space-y-1 list-decimal list-inside">
              <li>Upload your CV PDF to Google Drive, Dropbox, or similar service</li>
              <li>Get a direct download link (make sure it's publicly accessible)</li>
              <li>Paste the link in the "CV File URL" field above</li>
              <li>Set a descriptive filename</li>
              <li>Save the settings</li>
            </ol>
          </div>

          {/* Test Download */}
          {localCVSettings.cvFileUrl && (
            <div className="flex gap-3">
              <Button
                onClick={() => window.open(localCVSettings.cvFileUrl, '_blank')}
                variant="outline"
                className="border-red/30 text-red hover:bg-red/10"
              >
                <Download className="w-4 h-4 mr-2" />
                Test Download
              </Button>
            </div>
          )}

          {/* Save Button */}
          <div className="flex justify-end pt-4 border-t border-red/20">
            <Button
              onClick={handleSave}
              disabled={isUpdatingCV}
              className="bg-red hover:bg-red/80 text-white"
            >
              {isUpdatingCV ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save CV Settings
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case "general":
        return renderGeneralSettings();

      case "social":
        return renderSocialSettings();
      case "cv":
        return renderCVSettings();
      default:
        return (
          <div className="text-center py-12">
            <Settings className="w-12 h-12 text-red/50 mx-auto mb-4" />
            <p className="text-gray-400">{activeTab} settings coming soon...</p>
          </div>
        );
    }
  };

  return (
    <AdminLayout title="Settings">
      <div className="space-y-4 sm:space-y-6 max-w-full overflow-hidden">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4">
          <div>
            <h2 className="text-xl sm:text-2xl font-bold text-white">Settings</h2>
            <p className="text-gray-400 text-sm sm:text-base">
              Configure your admin panel and site settings
            </p>
          </div>
          <Button
            onClick={handleSave}
            disabled={isLoading}
            size="sm"
            className="bg-red hover:bg-red/80 text-xs sm:text-sm w-full sm:w-auto"
          >
            {isLoading ? (
              <div className="flex items-center gap-1 sm:gap-2">
                <div className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                <span className="hidden sm:inline">Saving...</span>
                <span className="sm:hidden">Save...</span>
              </div>
            ) : (
              <>
                <Save className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Save Changes</span>
                <span className="sm:hidden">Save</span>
              </>
            )}
          </Button>
        </div>

        {/* Settings Tabs */}
        <div className="bg-dark-lighter border border-red/20 rounded-xl overflow-hidden">
          {/* Mobile Tab Selector */}
          <div className="block sm:hidden border-b border-red/20 p-4">
            <select
              value={activeTab}
              onChange={(e) => setActiveTab(e.target.value)}
              className="w-full bg-dark-bg border border-red/20 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-red/50"
            >
              {settingsTabs.map((tab) => (
                <option key={tab.id} value={tab.id}>
                  {tab.label}
                </option>
              ))}
            </select>
          </div>

          {/* Desktop Tabs */}
          <div className="hidden sm:flex overflow-x-auto border-b border-red/20">
            {settingsTabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-4 lg:px-6 py-3 lg:py-4 whitespace-nowrap transition-all text-sm lg:text-base ${
                    activeTab === tab.id
                      ? "bg-red/20 text-red border-b-2 border-red"
                      : "text-gray-400 hover:text-white hover:bg-red/10"
                  }`}
                >
                  <Icon className="w-3 h-3 lg:w-4 lg:h-4" />
                  <span className="hidden md:inline">{tab.label}</span>
                  <span className="md:hidden">{tab.label.split(' ')[0]}</span>
                </button>
              );
            })}
          </div>

          <div className="p-3 sm:p-4 lg:p-6">{renderContent()}</div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminSettings;
