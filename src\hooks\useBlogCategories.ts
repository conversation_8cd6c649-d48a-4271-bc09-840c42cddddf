import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../services/supabase/client';
import { toast } from 'sonner';
import type { Database } from '../services/supabase/types';

type BlogCategory = Database['public']['Tables']['blog_categories']['Row'];
type BlogCategoryInsert = Database['public']['Tables']['blog_categories']['Insert'];
type BlogCategoryUpdate = Database['public']['Tables']['blog_categories']['Update'];

// Generate slug from name
const generateSlug = (name: string): string => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
};

export const useBlogCategories = () => {
  const queryClient = useQueryClient();

  // Fetch all blog categories
  const {
    data: categories,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['blog-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('blog_categories')
        .select('*')
        .order('name');

      if (error) throw error;
      return data as BlogCategory[];
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Get category by slug
  const getCategoryBySlug = async (slug: string): Promise<BlogCategory | null> => {
    const { data, error } = await supabase
      .from('blog_categories')
      .select('*')
      .eq('slug', slug)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }

    return data;
  };

  // Create category mutation
  const createCategoryMutation = useMutation({
    mutationFn: async (categoryData: BlogCategoryInsert) => {
      // Generate slug if not provided
      if (!categoryData.slug && categoryData.name) {
        categoryData.slug = generateSlug(categoryData.name);
      }

      const { data, error } = await supabase
        .from('blog_categories')
        .insert(categoryData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blog-categories'] });
      toast.success('Category created successfully!');
    },
    onError: (error: any) => {
      console.error('Error creating category:', error);
      if (error.code === '23505') {
        toast.error('Category name or slug already exists');
      } else {
        toast.error('Failed to create category');
      }
    },
  });

  // Update category mutation
  const updateCategoryMutation = useMutation({
    mutationFn: async ({ id, categoryData }: { id: string; categoryData: BlogCategoryUpdate }) => {
      // Generate slug if name changed
      if (categoryData.name && !categoryData.slug) {
        categoryData.slug = generateSlug(categoryData.name);
      }

      const { data, error } = await supabase
        .from('blog_categories')
        .update(categoryData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blog-categories'] });
      queryClient.invalidateQueries({ queryKey: ['blog-posts-all'] });
      queryClient.invalidateQueries({ queryKey: ['blog-posts-published'] });
      toast.success('Category updated successfully!');
    },
    onError: (error: any) => {
      console.error('Error updating category:', error);
      if (error.code === '23505') {
        toast.error('Category name or slug already exists');
      } else {
        toast.error('Failed to update category');
      }
    },
  });

  // Delete category mutation
  const deleteCategoryMutation = useMutation({
    mutationFn: async (id: string) => {
      // Check if category is being used by any posts
      const { data: posts, error: checkError } = await supabase
        .from('blog_post_categories')
        .select('id')
        .eq('category_id', id)
        .limit(1);

      if (checkError) throw checkError;

      if (posts && posts.length > 0) {
        throw new Error('Cannot delete category that is being used by blog posts');
      }

      const { error } = await supabase
        .from('blog_categories')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blog-categories'] });
      toast.success('Category deleted successfully!');
    },
    onError: (error: any) => {
      console.error('Error deleting category:', error);
      if (error.message.includes('being used by blog posts')) {
        toast.error('Cannot delete category that is being used by blog posts');
      } else {
        toast.error('Failed to delete category');
      }
    },
  });

  // Get categories with post counts
  const getCategoriesWithCounts = async () => {
    const { data, error } = await supabase
      .from('blog_categories')
      .select(`
        *,
        post_count:blog_post_categories(count)
      `);

    if (error) throw error;
    return data;
  };

  return {
    // Data
    categories,
    
    // Loading states
    isLoading,
    
    // Errors
    error,
    
    // Functions
    getCategoryBySlug,
    getCategoriesWithCounts,
    
    // Mutations
    createCategory: createCategoryMutation.mutate,
    updateCategory: updateCategoryMutation.mutate,
    deleteCategory: deleteCategoryMutation.mutate,
    
    // Mutation states
    isCreating: createCategoryMutation.isPending,
    isUpdating: updateCategoryMutation.isPending,
    isDeleting: deleteCategoryMutation.isPending,
  };
};
